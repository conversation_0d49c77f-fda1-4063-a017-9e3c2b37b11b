// API client for CMS integration
const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'https://cms.ashishkamat.com.np'

export interface Project {
  id: string
  title: string
  description: string
  longDescription?: string
  image?: string
  category: string
  technologies: string[]
  liveUrl?: string
  githubUrl?: string
  featured: boolean
  published: boolean
  order: number
  createdAt: string
  updatedAt: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  image?: string
  category: string
  tags: string[]
  published: boolean
  featured: boolean
  readTime?: number
  views: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
}

export interface Experience {
  id: string
  title: string
  company: string
  companyLogo?: string
  location: string
  period: string
  type: string
  description: string
  achievements: string[]
  technologies: string[]
  website?: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Education {
  id: string
  degree: string
  institution: string
  location: string
  period: string
  grade?: string
  description: string
  highlights: string[]
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Certification {
  id: string
  title: string
  issuer: string
  date: string
  credentialId?: string
  emoji?: string
  description: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Service {
  id: string
  title: string
  description: string
  features: string[]
  icon: string
  color: string
  bgColor: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface TechStack {
  id: string
  name: string
  logo: string
  color: string
  category: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Testimonial {
  id: string
  name: string
  role: string
  company: string
  content: string
  avatar?: string
  rating: number
  featured: boolean
  published: boolean
  order: number
  createdAt: string
  updatedAt: string
}

// API functions
export const api = {
  // Projects
  getProjects: async (): Promise<Project[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/projects`)
    if (!response.ok) {
      throw new Error('Failed to fetch projects')
    }
    return response.json()
  },

  getProject: async (id: string): Promise<Project> => {
    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch project')
    }
    return response.json()
  },

  // Blog Posts
  getBlogPosts: async (): Promise<BlogPost[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/blog`, {
      next: { revalidate: 60 } // Cache for 1 minute
    })
    if (!response.ok) {
      throw new Error('Failed to fetch blog posts')
    }
    return response.json()
  },

  getBlogPost: async (slug: string): Promise<BlogPost> => {
    const response = await fetch(`${CMS_BASE_URL}/api/blog/${slug}`, {
      next: { revalidate: 60 } // Cache for 1 minute
    })
    if (!response.ok) {
      throw new Error('Failed to fetch blog post')
    }
    return response.json()
  },

  // Get related blog posts
  getRelatedBlogPosts: async (currentPostId: string, limit: number = 3): Promise<BlogPost[]> => {
    try {
      const response = await fetch(`${CMS_BASE_URL}/api/blog/${currentPostId}/related?limit=${limit}`, {
        next: { revalidate: 300 } // Cache for 5 minutes
      })
      if (response.ok) {
        return response.json()
      }
    } catch (error) {
      console.log('Related posts endpoint not available, using fallback')
    }

    // Fallback: get all posts and filter client-side
    const allPosts = await api.getBlogPosts()
    const currentPost = allPosts.find(post => post.id === currentPostId)
    if (!currentPost) return []

    return getRelatedBlogPosts(allPosts, currentPost, limit)
  },

  // Services
  getServices: async (): Promise<Service[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/services`)
    if (!response.ok) {
      throw new Error('Failed to fetch services')
    }
    return response.json()
  },

  // Tech Stack
  getTechStack: async (): Promise<TechStack[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)
    if (!response.ok) {
      throw new Error('Failed to fetch tech stack')
    }
    return response.json()
  },

  // Testimonials
  getTestimonials: async (): Promise<Testimonial[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)
    if (!response.ok) {
      throw new Error('Failed to fetch testimonials')
    }
    return response.json()
  },

  // Experiences
  getExperiences: async (): Promise<Experience[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/experiences`)
    if (!response.ok) {
      throw new Error('Failed to fetch experiences')
    }
    return response.json()
  },

  // Education
  getEducation: async (): Promise<Education[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/education`)
    if (!response.ok) {
      throw new Error('Failed to fetch education')
    }
    return response.json()
  },

  // Certifications
  getCertifications: async (): Promise<Certification[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/certifications`)
    if (!response.ok) {
      throw new Error('Failed to fetch certifications')
    }
    return response.json()
  },
}

// Helper functions
export const getPublishedProjects = (projects: Project[]) =>
  projects.filter(project => project.published).sort((a, b) => a.order - b.order)

export const getFeaturedProjects = (projects: Project[]) =>
  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)

export const getPublishedBlogPosts = (posts: BlogPost[]) =>
  posts.filter(post => post.published).sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())

export const getFeaturedBlogPosts = (posts: BlogPost[]) =>
  posts.filter(post => post.published && post.featured).sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())

export const getRelatedBlogPosts = (posts: BlogPost[], currentPost: BlogPost, limit: number = 3) => {
  const publishedPosts = posts.filter(post => post.published && post.id !== currentPost.id);

  // Find posts with similar tags or category
  const relatedPosts = publishedPosts.filter(post =>
    post.category === currentPost.category ||
    post.tags.some(tag => currentPost.tags.includes(tag))
  );

  // If we have related posts, return up to limit
  if (relatedPosts.length > 0) {
    return relatedPosts.slice(0, limit);
  }

  // If no related posts found, return the most recent posts (excluding current)
  return publishedPosts
    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())
    .slice(0, limit);
}

export const getProjectsByCategory = (projects: Project[], category: string) => 
  category === 'All' 
    ? getPublishedProjects(projects)
    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)

export const getPublishedServices = (services: Service[]) => 
  services.filter(service => service.published).sort((a, b) => a.order - b.order)

export const getTechStackByCategory = (techStack: TechStack[]) => {
  const published = techStack.filter(tech => tech.published)
  return published.reduce((acc, tech) => {
    if (!acc[tech.category]) {
      acc[tech.category] = []
    }
    acc[tech.category].push(tech)
    return acc
  }, {} as Record<string, TechStack[]>)
}

export const getPublishedTestimonials = (testimonials: Testimonial[]) => 
  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)

export const getFeaturedTestimonials = (testimonials: Testimonial[]) =>
  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)

export const getPublishedExperiences = (experiences: Experience[]) =>
  experiences.filter(experience => experience.published).sort((a, b) => a.order - b.order)

export const getPublishedEducation = (education: Education[]) =>
  education.filter(edu => edu.published).sort((a, b) => a.order - b.order)

export const getPublishedCertifications = (certifications: Certification[]) =>
  certifications.filter(cert => cert.published).sort((a, b) => a.order - b.order)
