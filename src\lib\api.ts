// API client for CMS integration
const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3000'

// Mock blog data until real backend is implemented
const MOCK_BLOG_POSTS: BlogPost[] = [
  {
    id: "1",
    title: "Building Scalable React Applications with TypeScript",
    slug: "building-scalable-react-applications-typescript",
    excerpt: "Learn how to build maintainable and scalable React applications using TypeScript, including best practices for project structure, component patterns, and state management.",
    content: `# Building Scalable React Applications with TypeScript

TypeScript has become an essential tool for building robust React applications. In this comprehensive guide, we'll explore the best practices for creating scalable React applications with TypeScript.

## Project Structure

A well-organized project structure is crucial for maintainability:

\`\`\`
src/
  components/
    ui/
    features/
  hooks/
  utils/
  types/
  pages/
\`\`\`

## Component Patterns

### Functional Components with TypeScript

\`\`\`typescript
interface ButtonProps {
  variant: 'primary' | 'secondary';
  children: React.ReactNode;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({ variant, children, onClick }) => {
  return (
    <button className={\`btn btn-\${variant}\`} onClick={onClick}>
      {children}
    </button>
  );
};
\`\`\`

## State Management

For complex applications, consider using Zustand or Redux Toolkit with TypeScript for type-safe state management.

## Conclusion

TypeScript provides excellent developer experience and helps catch errors early in the development process.`,
    image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop",
    category: "React",
    tags: ["React", "TypeScript", "Frontend", "Development"],
    published: true,
    featured: true,
    readTime: 8,
    views: 1250,
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
    publishedAt: "2024-01-15T10:00:00Z"
  },
  {
    id: "2",
    title: "Next.js 14 App Router: Complete Guide",
    slug: "nextjs-14-app-router-complete-guide",
    excerpt: "Explore the new App Router in Next.js 14, including server components, streaming, and the latest features for building modern web applications.",
    content: `# Next.js 14 App Router: Complete Guide

Next.js 14 introduces significant improvements to the App Router, making it more powerful and developer-friendly.

## Key Features

- Server Components by default
- Improved streaming
- Enhanced caching strategies
- Better TypeScript support

## Getting Started

\`\`\`bash
npx create-next-app@latest my-app --typescript --tailwind --eslint
\`\`\`

## Server Components

Server Components run on the server and can directly access databases and APIs:

\`\`\`typescript
async function BlogPost({ params }: { params: { slug: string } }) {
  const post = await getPost(params.slug);
  return <article>{post.content}</article>;
}
\`\`\`

## Conclusion

The App Router provides a more intuitive and powerful way to build Next.js applications.`,
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop",
    category: "Next.js",
    tags: ["Next.js", "React", "App Router", "Server Components"],
    published: true,
    featured: true,
    readTime: 12,
    views: 980,
    createdAt: "2024-01-10T14:30:00Z",
    updatedAt: "2024-01-10T14:30:00Z",
    publishedAt: "2024-01-10T14:30:00Z"
  },
  {
    id: "3",
    title: "Mastering CSS Grid and Flexbox in 2024",
    slug: "mastering-css-grid-flexbox-2024",
    excerpt: "A comprehensive guide to modern CSS layout techniques using Grid and Flexbox, with practical examples and best practices for responsive design.",
    content: `# Mastering CSS Grid and Flexbox in 2024

CSS Grid and Flexbox are powerful layout systems that have revolutionized web design. Let's explore how to use them effectively.

## CSS Grid Basics

CSS Grid is perfect for two-dimensional layouts:

\`\`\`css
.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}
\`\`\`

## Flexbox for One-Dimensional Layouts

Flexbox excels at one-dimensional layouts:

\`\`\`css
.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}
\`\`\`

## When to Use Which

- Use Grid for complex layouts
- Use Flexbox for component-level layouts
- Combine both for maximum flexibility

## Conclusion

Mastering both Grid and Flexbox will make you a more effective frontend developer.`,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop",
    category: "CSS",
    tags: ["CSS", "Grid", "Flexbox", "Layout", "Responsive Design"],
    published: true,
    featured: false,
    readTime: 6,
    views: 750,
    createdAt: "2024-01-05T09:15:00Z",
    updatedAt: "2024-01-05T09:15:00Z",
    publishedAt: "2024-01-05T09:15:00Z"
  },
  {
    id: "4",
    title: "Modern JavaScript ES2024 Features",
    slug: "modern-javascript-es2024-features",
    excerpt: "Discover the latest JavaScript features in ES2024, including new array methods, improved async/await patterns, and enhanced developer experience.",
    content: `# Modern JavaScript ES2024 Features

JavaScript continues to evolve with new features that improve developer productivity and code quality.

## New Array Methods

ES2024 introduces several new array methods:

\`\`\`javascript
// Array.prototype.toSorted()
const numbers = [3, 1, 4, 1, 5];
const sorted = numbers.toSorted(); // [1, 1, 3, 4, 5]

// Array.prototype.with()
const newArray = numbers.with(0, 10); // [10, 1, 4, 1, 5]
\`\`\`

## Enhanced Async Patterns

\`\`\`javascript
// Top-level await in modules
const data = await fetch('/api/data').then(r => r.json());

// Promise.withResolvers()
const { promise, resolve, reject } = Promise.withResolvers();
\`\`\`

## Conclusion

These new features make JavaScript more powerful and developer-friendly.`,
    image: "https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=800&h=400&fit=crop",
    category: "JavaScript",
    tags: ["JavaScript", "ES2024", "Modern JS", "Features"],
    published: true,
    featured: false,
    readTime: 5,
    views: 620,
    createdAt: "2023-12-28T16:45:00Z",
    updatedAt: "2023-12-28T16:45:00Z",
    publishedAt: "2023-12-28T16:45:00Z"
  },
  {
    id: "5",
    title: "Building RESTful APIs with Node.js and Express",
    slug: "building-restful-apis-nodejs-express",
    excerpt: "Learn how to build robust and scalable RESTful APIs using Node.js and Express, including authentication, validation, and best practices.",
    content: `# Building RESTful APIs with Node.js and Express

Building APIs is a fundamental skill for backend developers. Let's explore how to create robust APIs with Node.js and Express.

## Setting Up Express

\`\`\`javascript
const express = require('express');
const app = express();

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
\`\`\`

## Creating Routes

\`\`\`javascript
// GET /api/users
app.get('/api/users', async (req, res) => {
  try {
    const users = await User.find();
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// POST /api/users
app.post('/api/users', async (req, res) => {
  try {
    const user = new User(req.body);
    await user.save();
    res.status(201).json(user);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
\`\`\`

## Authentication and Middleware

\`\`\`javascript
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.sendStatus(401);
  }

  jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};
\`\`\`

## Conclusion

Express provides a solid foundation for building APIs with Node.js.`,
    image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=800&h=400&fit=crop",
    category: "Backend",
    tags: ["Node.js", "Express", "API", "Backend", "REST"],
    published: true,
    featured: false,
    readTime: 10,
    views: 890,
    createdAt: "2023-12-20T11:20:00Z",
    updatedAt: "2023-12-20T11:20:00Z",
    publishedAt: "2023-12-20T11:20:00Z"
  }
];

export interface Project {
  id: string
  title: string
  description: string
  longDescription?: string
  image?: string
  category: string
  technologies: string[]
  liveUrl?: string
  githubUrl?: string
  featured: boolean
  published: boolean
  order: number
  createdAt: string
  updatedAt: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  image?: string
  category: string
  tags: string[]
  published: boolean
  featured: boolean
  readTime?: number
  views: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
}

export interface Experience {
  id: string
  title: string
  company: string
  companyLogo?: string
  location: string
  period: string
  type: string
  description: string
  achievements: string[]
  technologies: string[]
  website?: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Education {
  id: string
  degree: string
  institution: string
  location: string
  period: string
  grade?: string
  description: string
  highlights: string[]
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Certification {
  id: string
  title: string
  issuer: string
  date: string
  credentialId?: string
  emoji?: string
  description: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Service {
  id: string
  title: string
  description: string
  features: string[]
  icon: string
  color: string
  bgColor: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface TechStack {
  id: string
  name: string
  logo: string
  color: string
  category: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Testimonial {
  id: string
  name: string
  role: string
  company: string
  content: string
  avatar?: string
  rating: number
  featured: boolean
  published: boolean
  order: number
  createdAt: string
  updatedAt: string
}

// API functions
export const api = {
  // Projects
  getProjects: async (): Promise<Project[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/projects`)
    if (!response.ok) {
      throw new Error('Failed to fetch projects')
    }
    return response.json()
  },

  getProject: async (id: string): Promise<Project> => {
    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch project')
    }
    return response.json()
  },

  // Blog Posts
  getBlogPosts: async (): Promise<BlogPost[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Return published posts only
    return MOCK_BLOG_POSTS.filter(post => post.published)
      .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime());
  },

  getBlogPost: async (slug: string): Promise<BlogPost> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));

    const post = MOCK_BLOG_POSTS.find(post => post.slug === slug && post.published);
    if (!post) {
      throw new Error('Blog post not found');
    }
    return post;
  },

  // Services
  getServices: async (): Promise<Service[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/services`)
    if (!response.ok) {
      throw new Error('Failed to fetch services')
    }
    return response.json()
  },

  // Tech Stack
  getTechStack: async (): Promise<TechStack[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)
    if (!response.ok) {
      throw new Error('Failed to fetch tech stack')
    }
    return response.json()
  },

  // Testimonials
  getTestimonials: async (): Promise<Testimonial[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)
    if (!response.ok) {
      throw new Error('Failed to fetch testimonials')
    }
    return response.json()
  },

  // Experiences
  getExperiences: async (): Promise<Experience[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/experiences`)
    if (!response.ok) {
      throw new Error('Failed to fetch experiences')
    }
    return response.json()
  },

  // Education
  getEducation: async (): Promise<Education[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/education`)
    if (!response.ok) {
      throw new Error('Failed to fetch education')
    }
    return response.json()
  },

  // Certifications
  getCertifications: async (): Promise<Certification[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/certifications`)
    if (!response.ok) {
      throw new Error('Failed to fetch certifications')
    }
    return response.json()
  },
}

// Helper functions
export const getPublishedProjects = (projects: Project[]) =>
  projects.filter(project => project.published).sort((a, b) => a.order - b.order)

export const getFeaturedProjects = (projects: Project[]) =>
  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)

export const getPublishedBlogPosts = (posts: BlogPost[]) =>
  posts.filter(post => post.published).sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())

export const getFeaturedBlogPosts = (posts: BlogPost[]) =>
  posts.filter(post => post.published && post.featured).sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())

export const getRelatedBlogPosts = (posts: BlogPost[], currentPost: BlogPost, limit: number = 3) => {
  const publishedPosts = posts.filter(post => post.published && post.id !== currentPost.id);

  // Find posts with similar tags or category
  const relatedPosts = publishedPosts.filter(post =>
    post.category === currentPost.category ||
    post.tags.some(tag => currentPost.tags.includes(tag))
  );

  // If we have related posts, return up to limit
  if (relatedPosts.length > 0) {
    return relatedPosts.slice(0, limit);
  }

  // If no related posts found, return the most recent posts (excluding current)
  return publishedPosts
    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())
    .slice(0, limit);
}

export const getProjectsByCategory = (projects: Project[], category: string) => 
  category === 'All' 
    ? getPublishedProjects(projects)
    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)

export const getPublishedServices = (services: Service[]) => 
  services.filter(service => service.published).sort((a, b) => a.order - b.order)

export const getTechStackByCategory = (techStack: TechStack[]) => {
  const published = techStack.filter(tech => tech.published)
  return published.reduce((acc, tech) => {
    if (!acc[tech.category]) {
      acc[tech.category] = []
    }
    acc[tech.category].push(tech)
    return acc
  }, {} as Record<string, TechStack[]>)
}

export const getPublishedTestimonials = (testimonials: Testimonial[]) => 
  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)

export const getFeaturedTestimonials = (testimonials: Testimonial[]) =>
  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)

export const getPublishedExperiences = (experiences: Experience[]) =>
  experiences.filter(experience => experience.published).sort((a, b) => a.order - b.order)

export const getPublishedEducation = (education: Education[]) =>
  education.filter(edu => edu.published).sort((a, b) => a.order - b.order)

export const getPublishedCertifications = (certifications: Certification[]) =>
  certifications.filter(cert => cert.published).sort((a, b) => a.order - b.order)
