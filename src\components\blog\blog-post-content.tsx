"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import "highlight.js/styles/github-dark.css";
import { Progress } from "@/components/ui/progress";
import { type BlogPost } from "@/lib/api";

interface CodeProps extends React.HTMLAttributes<HTMLElement> {
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

// Function to detect if content is HTML (from rich text editor) or Markdown
function isHtmlContent(content: string): boolean {
  // Check for common HTML tags that wouldn't be in markdown
  const htmlTags = /<(div|span|p|h[1-6]|strong|em|ul|ol|li|table|tr|td|th|img|a|blockquote|pre|code)[^>]*>/i;
  return htmlTags.test(content);
}

// Function to safely render HTML content
function renderHtmlContent(content: string) {
  // Process content to ensure proper line breaks and spacing
  const processedContent = content
    .replace(/<p><\/p>/g, '<div class="mb-4"></div>') // Replace empty paragraphs with spacing
    .replace(/<p>\s*<\/p>/g, '<div class="mb-4"></div>') // Replace empty paragraphs with whitespace
    .replace(/\n\n/g, '<br><br>') // Convert double newlines to double breaks
    .replace(/\n/g, '<br>') // Convert single newlines to breaks
    .replace(/<\/p><p>/g, '</p><div class="mb-2"></div><p>') // Add spacing between paragraphs
    .replace(/<\/h([1-6])><p>/g, '</h$1><div class="mb-4"></div><p>') // Add spacing after headings
    .replace(/<\/ul><p>/g, '</ul><div class="mb-4"></div><p>') // Add spacing after lists
    .replace(/<\/ol><p>/g, '</ol><div class="mb-4"></div><p>'); // Add spacing after ordered lists

  return (
    <div
      className="prose prose-lg dark:prose-invert max-w-none
        prose-headings:font-bold prose-headings:text-foreground prose-headings:mt-8 prose-headings:mb-6
        prose-p:text-muted-foreground prose-p:leading-relaxed prose-p:mb-6
        prose-a:text-primary prose-a:no-underline hover:prose-a:underline
        prose-strong:text-foreground prose-strong:font-semibold
        prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm
        prose-pre:bg-muted prose-pre:border prose-pre:border-border prose-pre:p-4 prose-pre:rounded-lg prose-pre:mb-6
        prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:my-6
        prose-ul:list-disc prose-ul:ml-6 prose-ul:mb-6 prose-ul:space-y-2
        prose-ol:list-decimal prose-ol:ml-6 prose-ol:mb-6 prose-ol:space-y-2
        prose-li:text-muted-foreground prose-li:leading-relaxed
        prose-table:border-collapse prose-table:border prose-table:border-border prose-table:my-6
        prose-th:border prose-th:border-border prose-th:bg-muted prose-th:p-3 prose-th:font-semibold
        prose-td:border prose-td:border-border prose-td:p-3
        [&>*]:mb-6 [&>h1]:mt-10 [&>h2]:mt-8 [&>h3]:mt-6 [&>h4]:mt-4
        [&>p:empty]:hidden [&>br+br]:mb-4
        [&_p]:mb-4 [&_h1]:mb-6 [&_h2]:mb-5 [&_h3]:mb-4 [&_ul]:mb-6 [&_ol]:mb-6"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}

interface BlogPostContentProps {
  post: BlogPost;
}

export function BlogPostContent({ post }: BlogPostContentProps) {
  const [readingProgress, setReadingProgress] = useState(0);

  useEffect(() => {
    const updateReadingProgress = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      setReadingProgress(Math.min(100, Math.max(0, progress)));
    };

    window.addEventListener("scroll", updateReadingProgress);
    return () => window.removeEventListener("scroll", updateReadingProgress);
  }, []);

  return (
    <div className="relative">
      {/* Reading Progress */}
      <div className="fixed top-16 left-0 right-0 z-40 bg-background/80 backdrop-blur-sm border-b border-border">
        <Progress value={readingProgress} className="h-1 rounded-none" />
      </div>

      {/* Article Content */}
      <motion.article
        className="prose prose-lg dark:prose-invert max-w-none"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
      >
        {isHtmlContent(post.content) ? (
          renderHtmlContent(post.content)
        ) : (
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeHighlight, rehypeRaw]}
            components={{
              h1: ({ children }) => (
                <h1 className="text-3xl font-bold mt-12 mb-6 first:mt-0 scroll-mt-20">
                  {children}
                </h1>
              ),
              h2: ({ children }) => (
                <h2 className="text-2xl font-bold mt-10 mb-4 scroll-mt-20">
                  {children}
                </h2>
              ),
              h3: ({ children }) => (
                <h3 className="text-xl font-bold mt-8 mb-3 scroll-mt-20">
                  {children}
                </h3>
              ),
              p: ({ children }) => (
                <p className="mb-6 leading-relaxed text-muted-foreground">
                  {children}
                </p>
              ),
            ul: ({ children }) => (
              <ul className="mb-6 space-y-2 list-disc list-inside">
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol className="mb-6 space-y-2 list-decimal list-inside">
                {children}
              </ol>
            ),
            li: ({ children }) => (
              <li className="text-muted-foreground">
                {children}
              </li>
            ),
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-primary pl-6 my-6 italic text-muted-foreground bg-muted/50 py-4 rounded-r-lg">
                {children}
              </blockquote>
            ),
            code: ({ inline, className, children, ...props }: CodeProps) => {
              const match = /language-(\w+)/.exec(className || '');
              
              if (!inline && match) {
                return (
                  <div className="relative group">
                    <div className="absolute top-3 right-3 text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                      {match[1]}
                    </div>
                    <pre className="bg-muted p-4 rounded-lg overflow-x-auto border border-border my-6">
                      <code className={className} {...props}>
                        {children}
                      </code>
                    </pre>
                  </div>
                );
              }
              
              return (
                <code 
                  className="bg-muted px-1.5 py-0.5 rounded text-sm font-mono border border-border" 
                  {...props}
                >
                  {children}
                </code>
              );
            },
            pre: ({ children }) => (
              <div className="not-prose">
                {children}
              </div>
            ),
            a: ({ href, children }) => (
              <a 
                href={href}
                className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors duration-200"
                target={href?.startsWith('http') ? '_blank' : undefined}
                rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
              >
                {children}
              </a>
            ),
            img: ({ src, alt }) => (
              <div className="my-8">
                <Image
                  src={typeof src === 'string' ? src : ''}
                  alt={alt || ''}
                  width={800}
                  height={400}
                  className="rounded-lg border border-border w-full"
                />
                {alt && (
                  <p className="text-center text-sm text-muted-foreground mt-2 italic">
                    {alt}
                  </p>
                )}
              </div>
            ),
            table: ({ children }) => (
              <div className="overflow-x-auto my-6">
                <table className="w-full border-collapse border border-border rounded-lg">
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className="border border-border px-4 py-2 bg-muted font-semibold text-left">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="border border-border px-4 py-2">
                {children}
              </td>
            ),
            hr: () => (
              <hr className="my-8 border-border" />
            ),
          }}
        >
          {post.content}
        </ReactMarkdown>
        )}
      </motion.article>

      {/* Article Summary */}
      {post.excerpt && (
        <div className="mt-12 p-6 bg-muted/50 rounded-lg border border-border">
          <h3 className="font-semibold mb-4">Article Summary</h3>
          <p className="text-sm text-muted-foreground">
            {post.excerpt}
          </p>
        </div>
      )}
    </div>
  );
}
