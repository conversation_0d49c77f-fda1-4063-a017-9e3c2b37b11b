import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { BlogPostHeader } from "@/components/blog/blog-post-header";
import { BlogPostContent } from "@/components/blog/blog-post-content";
import { BlogPostSidebar } from "@/components/blog/blog-post-sidebar";
import { RelatedPosts } from "@/components/blog/related-posts";
import { BlogPostNavigation } from "@/components/blog/blog-post-navigation";
import { api } from "@/lib/api";

// Get blog post from CMS
const getBlogPost = async (slug: string) => {
  try {
    return await api.getBlogPost(slug);
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
};

interface BlogPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    return {
      title: "Post Not Found - Ashish Kamat",
      description: "The requested blog post could not be found.",
    };
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://ashishkamat.com.np';
  const postUrl = `${baseUrl}/blog/${post.slug}`;
  const imageUrl = post.image || `${baseUrl}/og-image.jpg`;

  return {
    title: `${post.title} - Ashish Kamat`,
    description: post.excerpt,
    keywords: post.tags.join(', '),
    authors: [{ name: 'Ashish Kamat', url: baseUrl }],
    creator: 'Ashish Kamat',
    publisher: 'Ashish Kamat',
    alternates: {
      canonical: postUrl,
    },
    openGraph: {
      title: post.title,
      description: post.excerpt,
      url: postUrl,
      siteName: 'Ashish Kamat - Portfolio',
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      locale: 'en_US',
      type: 'article',
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt,
      authors: ['Ashish Kamat'],
      section: post.category,
      tags: post.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt,
      creator: '@ashishkamat',
      images: [imageUrl],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://ashishkamat.com.np';
  const postUrl = `${baseUrl}/blog/${post.slug}`;
  const imageUrl = post.image || `${baseUrl}/og-image.jpg`;

  // JSON-LD structured data for SEO
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: imageUrl,
    author: {
      '@type': 'Person',
      name: 'Ashish Kamat',
      url: baseUrl,
    },
    publisher: {
      '@type': 'Person',
      name: 'Ashish Kamat',
      url: baseUrl,
    },
    datePublished: post.publishedAt,
    dateModified: post.updatedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': postUrl,
    },
    url: postUrl,
    keywords: post.tags.join(', '),
    articleSection: post.category,
    wordCount: post.content.split(' ').length,
    timeRequired: `PT${post.readTime || 5}M`,
    inLanguage: 'en-US',
  };

  return (
    <div className="min-h-screen">
      {/* JSON-LD structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <Navigation />
      <main className="pt-16">
        <article>
          <BlogPostHeader post={post} />
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid lg:grid-cols-4 gap-12">
              <div className="lg:col-span-3">
                <BlogPostContent post={post} />
                {/* <BlogPostNavigation currentSlug={slug} /> */}
              </div>
              <div className="lg:col-span-1">
                <BlogPostSidebar post={post} />
              </div>
            </div>
          </div>
        </article>
        <RelatedPosts currentPost={post} />
      </main>
      <Footer />
    </div>
  );
}
