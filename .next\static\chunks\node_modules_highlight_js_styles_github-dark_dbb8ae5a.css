/* [project]/node_modules/highlight.js/styles/github-dark.css [app-client] (css) */
pre code.hljs {
  padding: 1em;
  display: block;
  overflow-x: auto;
}

code.hljs {
  padding: 3px 5px;
}

.hljs {
  color: #c9d1d9;
  background: #0d1117;
}

.hljs-doctag, .hljs-keyword, .hljs-meta .hljs-keyword, .hljs-template-tag, .hljs-template-variable, .hljs-type, .hljs-variable.language_ {
  color: #ff7b72;
}

.hljs-title, .hljs-title.class_, .hljs-title.class_.inherited__, .hljs-title.function_ {
  color: #d2a8ff;
}

.hljs-attr, .hljs-attribute, .hljs-literal, .hljs-meta, .hljs-number, .hljs-operator, .hljs-variable, .hljs-selector-attr, .hljs-selector-class, .hljs-selector-id {
  color: #79c0ff;
}

.hljs-regexp, .hljs-string, .hljs-meta .hljs-string {
  color: #a5d6ff;
}

.hljs-built_in, .hljs-symbol {
  color: #ffa657;
}

.hljs-comment, .hljs-code, .hljs-formula {
  color: #8b949e;
}

.hljs-name, .hljs-quote, .hljs-selector-tag, .hljs-selector-pseudo {
  color: #7ee787;
}

.hljs-subst {
  color: #c9d1d9;
}

.hljs-section {
  color: #1f6feb;
  font-weight: bold;
}

.hljs-bullet {
  color: #f2cc60;
}

.hljs-emphasis {
  color: #c9d1d9;
  font-style: italic;
}

.hljs-strong {
  color: #c9d1d9;
  font-weight: bold;
}

.hljs-addition {
  color: #aff5b4;
  background-color: #033a16;
}

.hljs-deletion {
  color: #ffdcd7;
  background-color: #67060c;
}


/*# sourceMappingURL=node_modules_highlight_js_styles_github-dark_dbb8ae5a.css.map*/