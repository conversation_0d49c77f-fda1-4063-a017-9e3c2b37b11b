/* [project]/node_modules/react-vertical-timeline-component/style.min.css [app-client] (css) */
.vertical-timeline * {
  box-sizing: border-box;
}

.vertical-timeline {
  width: 95%;
  max-width: 1170px;
  margin: 0 auto;
  padding: 2em 0;
  position: relative;
}

.vertical-timeline:after {
  content: "";
  clear: both;
  display: table;
}

.vertical-timeline:before {
  content: "";
  background: var(--line-color);
  width: 4px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 18px;
}

.vertical-timeline.vertical-timeline--one-column-right:before {
  right: 18px;
  left: unset;
}

@media only screen and (width >= 1170px) {
  .vertical-timeline.vertical-timeline--two-columns {
    width: 90%;
  }

  .vertical-timeline.vertical-timeline--two-columns:before {
    margin-left: -2px;
    left: 50%;
  }
}

.vertical-timeline-element {
  margin: 2em 0;
  position: relative;
}

.vertical-timeline-element > div {
  min-height: 1px;
}

.vertical-timeline-element:after {
  content: "";
  clear: both;
  display: table;
}

.vertical-timeline-element:first-child {
  margin-top: 0;
}

.vertical-timeline-element:last-child {
  margin-bottom: 0;
}

@media only screen and (width >= 1170px) {
  .vertical-timeline-element {
    margin: 4em 0;
  }

  .vertical-timeline-element:first-child {
    margin-top: 0;
  }

  .vertical-timeline-element:last-child {
    margin-bottom: 0;
  }
}

.vertical-timeline-element-icon {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  position: absolute;
  top: 0;
  left: 0;
}

.vertical-timeline-element-icon.shadow-size-small {
  box-shadow: 0 0 0 2px #fff, inset 0 1px #00000014, 0 2px 0 4px #0000000d;
}

.vertical-timeline-element-icon.shadow-size-medium {
  box-shadow: 0 0 0 3px #fff, inset 0 2px #00000014, 0 3px 0 5px #0000000d;
}

.vertical-timeline-element-icon.shadow-size-large {
  box-shadow: 0 0 0 4px #fff, inset 0 2px #00000014, 0 4px 0 6px #0000000d;
}

.vertical-timeline--one-column-right .vertical-timeline-element-icon {
  right: 0;
  left: unset;
}

.vertical-timeline-element-icon svg {
  width: 24px;
  height: 24px;
  margin-top: -12px;
  margin-left: -12px;
  display: block;
  position: relative;
  top: 50%;
  left: 50%;
}

@media only screen and (width >= 1170px) {
  .vertical-timeline--two-columns .vertical-timeline-element-icon {
    width: 60px;
    height: 60px;
    margin-left: -30px;
    left: 50%;
  }
}

.vertical-timeline-element-icon {
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0);
}

.vertical-timeline--animate .vertical-timeline-element-icon.is-hidden {
  visibility: hidden;
}

.vertical-timeline--animate .vertical-timeline-element-icon.bounce-in {
  visibility: visible;
  -webkit-animation: .6s cd-bounce-1;
  -moz-animation: .6s cd-bounce-1;
  animation: .6s cd-bounce-1;
}

@-webkit-keyframes cd-bounce-1 {
  0% {
    opacity: 0;
    -webkit-transform: scale(.5);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale(1.2);
  }

  100% {
    -webkit-transform: scale(1);
  }
}

@-moz-keyframes cd-bounce-1 {
  0% {
    opacity: 0;
    -moz-transform: scale(.5);
  }

  60% {
    opacity: 1;
    -moz-transform: scale(1.2);
  }

  100% {
    -moz-transform: scale(1);
  }
}

@keyframes cd-bounce-1 {
  0% {
    opacity: 0;
    -webkit-transform: scale(.5);
    -moz-transform: scale(.5);
    -ms-transform: scale(.5);
    -o-transform: scale(.5);
    transform: scale(.5);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
  }

  100% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
}

.vertical-timeline-element-content {
  background: #fff;
  border-radius: .25em;
  margin-left: 60px;
  padding: 1em;
  position: relative;
  box-shadow: 0 3px #ddd;
}

.vertical-timeline--one-column-right .vertical-timeline-element-content {
  margin-right: 60px;
  margin-left: unset;
}

.vertical-timeline-element--no-children .vertical-timeline-element-content {
  box-shadow: none;
  background: none;
}

.vertical-timeline-element-content:after {
  content: "";
  clear: both;
  display: table;
}

.vertical-timeline-element-content h2 {
  color: #303e49;
}

.vertical-timeline-element-content .vertical-timeline-element-date, .vertical-timeline-element-content p {
  font-size: .8125rem;
  font-weight: 500;
}

.vertical-timeline-element-content .vertical-timeline-element-date {
  display: inline-block;
}

.vertical-timeline-element-content p {
  margin: 1em 0 0;
  line-height: 1.6;
}

.vertical-timeline-element-title, .vertical-timeline-element-subtitle {
  margin: 0;
}

.vertical-timeline-element-content .vertical-timeline-element-date {
  float: left;
  opacity: .7;
  padding: .8em 0;
}

.vertical-timeline-element-content-arrow {
  content: "";
  border: 7px solid #0000;
  border-right-color: #fff;
  width: 0;
  height: 0;
  position: absolute;
  top: 16px;
  right: 100%;
}

.vertical-timeline--one-column-right .vertical-timeline-element-content-arrow {
  content: "";
  top: 16px;
  right: unset;
  border: 7px solid #0000;
  border-left-color: #fff;
  width: 0;
  height: 0;
  position: absolute;
  left: 100%;
}

.vertical-timeline--one-column-right .vertical-timeline-element-content:before {
  border-left: 7px solid #fff;
  border-right: unset;
  left: 100%;
}

.vertical-timeline-element--no-children .vertical-timeline-element-content:before, .vertical-timeline-element--no-children .vertical-timeline-element-content-arrow {
  display: none;
}

@media only screen and (width >= 768px) {
  .vertical-timeline-element-content h2 {
    font-size: 1.25rem;
  }

  .vertical-timeline-element-content p {
    font-size: 1rem;
  }

  .vertical-timeline-element-content .vertical-timeline-element-date {
    font-size: .875rem;
  }
}

@media only screen and (width >= 1170px) {
  .vertical-timeline--two-columns .vertical-timeline-element-content {
    width: 44%;
    margin-left: 0;
    padding: 1.5em;
  }

  .vertical-timeline--two-columns .vertical-timeline-element-content-arrow {
    top: 24px;
    left: 100%;
    transform: rotate(180deg);
  }

  .vertical-timeline--two-columns .vertical-timeline-element-content .vertical-timeline-element-date {
    width: 100%;
    font-size: 1rem;
    position: absolute;
    top: 6px;
    left: 124%;
  }

  .vertical-timeline--two-columns .vertical-timeline-element.vertical-timeline-element--right .vertical-timeline-element-content, .vertical-timeline--two-columns .vertical-timeline-element:nth-child(2n):not(.vertical-timeline-element--left) .vertical-timeline-element-content {
    float: right;
  }

  .vertical-timeline--two-columns .vertical-timeline-element.vertical-timeline-element--right .vertical-timeline-element-content-arrow, .vertical-timeline--two-columns .vertical-timeline-element:nth-child(2n):not(.vertical-timeline-element--left) .vertical-timeline-element-content-arrow {
    top: 24px;
    left: auto;
    right: 100%;
    transform: rotate(0);
  }

  .vertical-timeline--one-column-right .vertical-timeline-element.vertical-timeline-element--right .vertical-timeline-element-content-arrow, .vertical-timeline--one-column-right .vertical-timeline-element:nth-child(2n):not(.vertical-timeline-element--left) .vertical-timeline-element-content-arrow {
    top: 24px;
    left: 100%;
    right: auto;
    transform: rotate(0);
  }

  .vertical-timeline--two-columns .vertical-timeline-element.vertical-timeline-element--right .vertical-timeline-element-content .vertical-timeline-element-date, .vertical-timeline--two-columns .vertical-timeline-element:nth-child(2n):not(.vertical-timeline-element--left) .vertical-timeline-element-content .vertical-timeline-element-date {
    text-align: right;
    left: auto;
    right: 124%;
  }
}

.vertical-timeline--animate .vertical-timeline-element-content.is-hidden {
  visibility: hidden;
}

.vertical-timeline--animate .vertical-timeline-element-content.bounce-in {
  visibility: visible;
  -webkit-animation: .6s cd-bounce-2;
  -moz-animation: .6s cd-bounce-2;
  animation: .6s cd-bounce-2;
}

@media only screen and (width >= 1170px) {
  .vertical-timeline--two-columns.vertical-timeline--animate .vertical-timeline-element.vertical-timeline-element--right .vertical-timeline-element-content.bounce-in, .vertical-timeline--two-columns.vertical-timeline--animate .vertical-timeline-element:nth-child(2n):not(.vertical-timeline-element--left) .vertical-timeline-element-content.bounce-in {
    -webkit-animation: .6s cd-bounce-2-inverse;
    -moz-animation: .6s cd-bounce-2-inverse;
    animation: .6s cd-bounce-2-inverse;
  }
}

@media only screen and (width <= 1169px) {
  .vertical-timeline--animate .vertical-timeline-element-content.bounce-in {
    visibility: visible;
    -webkit-animation: .6s cd-bounce-2-inverse;
    -moz-animation: .6s cd-bounce-2-inverse;
    animation: .6s cd-bounce-2-inverse;
  }
}

@-webkit-keyframes cd-bounce-2 {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100px);
  }

  60% {
    opacity: 1;
    -webkit-transform: translateX(20px);
  }

  100% {
    -webkit-transform: translateX(0);
  }
}

@-moz-keyframes cd-bounce-2 {
  0% {
    opacity: 0;
    -moz-transform: translateX(-100px);
  }

  60% {
    opacity: 1;
    -moz-transform: translateX(20px);
  }

  100% {
    -moz-transform: translateX(0);
  }
}

@keyframes cd-bounce-2 {
  0% {
    opacity: 0;
    -webkit-transform: translateX(-100px);
    -moz-transform: translateX(-100px);
    -ms-transform: translateX(-100px);
    -o-transform: translateX(-100px);
    transform: translateX(-100px);
  }

  60% {
    opacity: 1;
    -webkit-transform: translateX(20px);
    -moz-transform: translateX(20px);
    -ms-transform: translateX(20px);
    -o-transform: translateX(20px);
    transform: translateX(20px);
  }

  100% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
}

@-webkit-keyframes cd-bounce-2-inverse {
  0% {
    opacity: 0;
    -webkit-transform: translateX(100px);
  }

  60% {
    opacity: 1;
    -webkit-transform: translateX(-20px);
  }

  100% {
    -webkit-transform: translateX(0);
  }
}

@-moz-keyframes cd-bounce-2-inverse {
  0% {
    opacity: 0;
    -moz-transform: translateX(100px);
  }

  60% {
    opacity: 1;
    -moz-transform: translateX(-20px);
  }

  100% {
    -moz-transform: translateX(0);
  }
}

@keyframes cd-bounce-2-inverse {
  0% {
    opacity: 0;
    -webkit-transform: translateX(100px);
    -moz-transform: translateX(100px);
    -ms-transform: translateX(100px);
    -o-transform: translateX(100px);
    transform: translateX(100px);
  }

  60% {
    opacity: 1;
    -webkit-transform: translateX(-20px);
    -moz-transform: translateX(-20px);
    -ms-transform: translateX(-20px);
    -o-transform: translateX(-20px);
    transform: translateX(-20px);
  }

  100% {
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    -o-transform: translateX(0);
    transform: translateX(0);
  }
}


/*# sourceMappingURL=node_modules_react-vertical-timeline-component_style_min_5dedb90c.css.map*/