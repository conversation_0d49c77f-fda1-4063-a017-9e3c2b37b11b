"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { api, type BlogPost } from "@/lib/api";

interface BlogPostNavigationProps {
  currentSlug: string;
}

interface NavigationPosts {
  previous: { slug: string; title: string } | null;
  next: { slug: string; title: string } | null;
}

// Get navigation posts from API
const getNavigationPosts = async (currentSlug: string): Promise<NavigationPosts> => {
  try {
    const allPosts = await api.getBlogPosts();
    const publishedPosts = allPosts.filter(post => post.published);

    // Sort by published date (newest first)
    publishedPosts.sort((a, b) =>
      new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime()
    );

    const currentIndex = publishedPosts.findIndex(post => post.slug === currentSlug);

    return {
      previous: currentIndex > 0 ? {
        slug: publishedPosts[currentIndex - 1].slug,
        title: publishedPosts[currentIndex - 1].title
      } : null,
      next: currentIndex < publishedPosts.length - 1 ? {
        slug: publishedPosts[currentIndex + 1].slug,
        title: publishedPosts[currentIndex + 1].title
      } : null
    };
  } catch (error) {
    console.error('Error fetching navigation posts:', error);
    return { previous: null, next: null };
  }
};

export function BlogPostNavigation({ currentSlug }: BlogPostNavigationProps) {
  const [navigationPosts, setNavigationPosts] = useState<NavigationPosts>({ previous: null, next: null });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNavigationPosts = async () => {
      try {
        setLoading(true);
        const posts = await getNavigationPosts(currentSlug);
        setNavigationPosts(posts);
      } catch (error) {
        console.error('Error fetching navigation posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchNavigationPosts();
  }, [currentSlug]);

  if (loading) {
    return (
      <div className="mt-16 pt-8 border-t border-border">
        <div className="grid md:grid-cols-2 gap-6">
          <div className="h-20 bg-muted/50 rounded-lg animate-pulse" />
          <div className="h-20 bg-muted/50 rounded-lg animate-pulse" />
        </div>
      </div>
    );
  }

  const { previous, next } = navigationPosts;

  if (!previous && !next) {
    return null;
  }

  return (
    <motion.div
      className="mt-16 pt-8 border-t border-border"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="grid md:grid-cols-2 gap-6">
        {/* Previous Post */}
        <div className="flex justify-start">
          {previous ? (
            <Card className="hover-lift group border-border/50 hover:border-border transition-all duration-300 w-full">
              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  asChild
                  className="h-auto p-0 flex flex-col items-start space-y-2 w-full"
                >
                  <Link href={`/blog/${previous.slug}`}>
                    <div className="flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors duration-300">
                      <ChevronLeft className="mr-1 h-4 w-4" />
                      Previous Article
                    </div>
                    <h3 className="text-left font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {previous.title}
                    </h3>
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div /> // Empty div to maintain grid layout
          )}
        </div>

        {/* Next Post */}
        <div className="flex justify-end">
          {next ? (
            <Card className="hover-lift group border-border/50 hover:border-border transition-all duration-300 w-full">
              <CardContent className="p-6">
                <Button
                  variant="ghost"
                  asChild
                  className="h-auto p-0 flex flex-col items-end space-y-2 w-full"
                >
                  <Link href={`/blog/${next.slug}`}>
                    <div className="flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors duration-300">
                      Next Article
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </div>
                    <h3 className="text-right font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2">
                      {next.title}
                    </h3>
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div /> // Empty div to maintain grid layout
          )}
        </div>
      </div>

      {/* Back to Blog */}
      <div className="text-center mt-8">
        <Button variant="outline" asChild>
          <Link href="/blog">
            ← Back to All Articles
          </Link>
        </Button>
      </div>
    </motion.div>
  );
}
