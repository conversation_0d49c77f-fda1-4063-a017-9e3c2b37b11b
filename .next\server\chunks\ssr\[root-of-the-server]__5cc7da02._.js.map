{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/download-utils.ts"], "sourcesContent": ["/**\r\n * Downloads the resume file\r\n * @param filename - Optional custom filename for the download\r\n */\r\nexport function downloadResume(filename: string = 'Ashish_Kamat_Resume.pdf') {\r\n  const link = document.createElement('a');\r\n  link.href = '/resume.pdf';\r\n  link.download = filename;\r\n  document.body.appendChild(link);\r\n  link.click();\r\n  document.body.removeChild(link);\r\n}\r\n\r\n/**\r\n * Opens the resume in a new tab\r\n */\r\nexport function viewResume() {\r\n  window.open('/resume.pdf', '_blank');\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AACM,SAAS,eAAe,WAAmB,yBAAyB;IACzE,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAKO,SAAS;IACd,OAAO,IAAI,CAAC,eAAe;AAC7B", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { downloadResume } from \"@/lib/download-utils\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<{ className?: string }>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => downloadResume(),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for &quot;{search}&quot;\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;AACA;AAzBA;;;;;;;;;;AA0CO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,mMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,4MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,wMAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,6BAA6B;YACvD,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;YAC3B,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;IACnB,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,MAAM;YAEX,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;gBAC9B,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;gBAC5B,EAAE,cAAc;gBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;oBACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;oBACtC,aAAa;oBACb,UAAU;gBACZ;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,8OAAC;wBAAI,WAAU;;4BAAyC;4BACzB;4BAAO;;;;;;6CAGtC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC;4CACb,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,8OAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AApBA;;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,sBAAsB;YACxB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC,wIAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ArrowDown, Download, Mail, Github, Linkedin, Twitter } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { downloadResume } from \"@/lib/download-utils\";\n\nconst roles = [\n  \"Full Stack Developer\",\n  \"UI/UX Designer\", \n  \"React Specialist\",\n  \"TypeScript Expert\",\n  \"Problem Solver\"\n];\n\nconst socialLinks = [\n  { icon: Github, href: \"https://github.com/ashishkamat\", label: \"GitHub\" },\n  { icon: Linkedin, href: \"https://linkedin.com/in/ashishkamat\", label: \"LinkedIn\" },\n  { icon: Twitter, href: \"https://twitter.com/ashishkamat\", label: \"Twitter\" },\n];\n\nexport function HeroSection() {\n  const [currentRole, setCurrentRole] = useState(0);\n  const [displayText, setDisplayText] = useState(\"\");\n  const [isTyping, setIsTyping] = useState(true);\n\n  useEffect(() => {\n    const role = roles[currentRole];\n    let index = 0;\n    \n    const typeText = () => {\n      if (index < role.length) {\n        setDisplayText(role.slice(0, index + 1));\n        index++;\n        setTimeout(typeText, 100);\n      } else {\n        setTimeout(() => {\n          setIsTyping(false);\n          setTimeout(() => {\n            setDisplayText(\"\");\n            setCurrentRole((prev) => (prev + 1) % roles.length);\n            setIsTyping(true);\n          }, 1000);\n        }, 2000);\n      }\n    };\n\n    if (isTyping) {\n      typeText();\n    }\n  }, [currentRole, isTyping]);\n\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 -z-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-float\" />\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"1s\" }} />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-green-500/10 rounded-full blur-3xl animate-float\" style={{ animationDelay: \"2s\" }} />\n      </div>\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 pt-16\">\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            className=\"space-y-6 lg:space-y-8 order-2 lg:order-1\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"space-y-4\">\n              <motion.p\n                className=\"text-base sm:text-lg text-muted-foreground\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                Hello, I&apos;m\n              </motion.p>\n\n              <motion.h1\n                className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <span className=\"gradient-text-blue\">Ashish Kamat</span>\n              </motion.h1>\n\n              <div className=\"h-12 sm:h-14 lg:h-16 flex items-center\">\n                <motion.h2\n                  className=\"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-semibold text-foreground/80\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.4 }}\n                >\n                  a{\" \"}\n                  <span className=\"gradient-text relative\">\n                    {displayText}\n                    <span className=\"animate-pulse\">|</span>\n                  </span>\n                </motion.h2>\n              </div>\n\n              <motion.p\n                className=\"text-base sm:text-lg text-muted-foreground max-w-2xl leading-relaxed\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.5 }}\n              >\n                I help founders and businesses turn ideas into seamless digital experiences.\n                From concept to deployment, I create modern web applications that make a difference.\n              </motion.p>\n            </div>\n\n            {/* CTA Buttons */}\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-3 sm:gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n            >\n              <Button asChild size=\"lg\" className=\"group w-full sm:w-auto\">\n                <Link href=\"/contact\">\n                  <Mail className=\"mr-2 h-4 w-4 group-hover:animate-bounce\" />\n                  Get In Touch\n                </Link>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"group w-full sm:w-auto\"\n                onClick={() => downloadResume()}\n              >\n                <Download className=\"mr-2 h-4 w-4 group-hover:animate-bounce\" />\n                Download CV\n              </Button>\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              className=\"flex justify-center sm:justify-start space-x-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.7 }}\n            >\n              {socialLinks.map((social, index) => {\n                const Icon = social.icon;\n                return (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"p-3 rounded-full bg-accent hover:bg-accent/80 transition-colors duration-200 group\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.8 + index * 0.1 }}\n                  >\n                    <Icon className=\"h-5 w-5 group-hover:text-primary transition-colors duration-200\" />\n                  </motion.a>\n                );\n              })}\n            </motion.div>\n          </motion.div>\n\n          {/* Profile Image */}\n          <motion.div\n            className=\"relative order-1 lg:order-2\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div className=\"relative w-64 h-64 sm:w-80 sm:h-80 lg:w-96 lg:h-96 mx-auto\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse-glow\" />\n              <div className=\"absolute inset-2 bg-background rounded-full overflow-hidden\">\n                <Image\n                  src=\"/ashish.png\"\n                  alt=\"Ashish Kamat\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1 }}\n        >\n          <motion.div\n            className=\"flex flex-col items-center space-y-2 text-muted-foreground\"\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          >\n            <span className=\"text-sm\">Scroll to explore</span>\n            <ArrowDown className=\"h-4 w-4\" />\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAkC,OAAO;IAAS;IACxE;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAuC,OAAO;IAAW;IACjF;QAAE,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAmC,OAAO;IAAU;CAC5E;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,KAAK,CAAC,YAAY;QAC/B,IAAI,QAAQ;QAEZ,MAAM,WAAW;YACf,IAAI,QAAQ,KAAK,MAAM,EAAE;gBACvB,eAAe,KAAK,KAAK,CAAC,GAAG,QAAQ;gBACrC;gBACA,WAAW,UAAU;YACvB,OAAO;gBACL,WAAW;oBACT,YAAY;oBACZ,WAAW;wBACT,eAAe;wBACf,eAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;wBAClD,YAAY;oBACd,GAAG;gBACL,GAAG;YACL;QACF;QAEA,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;QAAa;KAAS;IAE1B,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA+F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCAC5I,8OAAC;wBAAI,WAAU;wBAAsI,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGrL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAC1B;;;;;;0DAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAEzB,cAAA,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAGvC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDACR,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO;oDAAI;;wDAC1B;wDACG;sEACF,8OAAC;4DAAK,WAAU;;gEACb;8EACD,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;0DAKtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;0DAC1B;;;;;;;;;;;;kDAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,MAAK;gDAAK,WAAU;0DAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAA4C;;;;;;;;;;;;0DAKhE,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;;kEAE5B,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAA4C;;;;;;;;;;;;;kDAMpE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAExB,YAAY,GAAG,CAAC,CAAC,QAAQ;4CACxB,MAAM,OAAO,OAAO,IAAI;4CACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,MAAM,QAAQ;gDAAI;0DAEvC,cAAA,8OAAC;oDAAK,WAAU;;;;;;+CAXX,OAAO,KAAK;;;;;wCAcvB;;;;;;;;;;;;0CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAE;kCAEvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;;8CAE5C,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/api.ts"], "sourcesContent": ["// API client for CMS integration\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'https://cms.ashishkamat.com.np'\n\nexport interface Project {\n  id: string\n  title: string\n  description: string\n  longDescription?: string\n  image?: string\n  category: string\n  technologies: string[]\n  liveUrl?: string\n  githubUrl?: string\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  image?: string\n  category: string\n  tags: string[]\n  published: boolean\n  featured: boolean\n  readTime?: number\n  views: number\n  createdAt: string\n  updatedAt: string\n  publishedAt?: string\n}\n\nexport interface Experience {\n  id: string\n  title: string\n  company: string\n  companyLogo?: string\n  location: string\n  period: string\n  type: string\n  description: string\n  achievements: string[]\n  technologies: string[]\n  website?: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Education {\n  id: string\n  degree: string\n  institution: string\n  location: string\n  period: string\n  grade?: string\n  description: string\n  highlights: string[]\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Certification {\n  id: string\n  title: string\n  issuer: string\n  date: string\n  credentialId?: string\n  emoji?: string\n  description: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  icon: string\n  color: string\n  bgColor: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TechStack {\n  id: string\n  name: string\n  logo: string\n  color: string\n  category: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  role: string\n  company: string\n  content: string\n  avatar?: string\n  rating: number\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\n// API functions\nexport const api = {\n  // Projects\n  getProjects: async (): Promise<Project[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch projects')\n    }\n    return response.json()\n  },\n\n  getProject: async (id: string): Promise<Project> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch project')\n    }\n    return response.json()\n  },\n\n  // Blog Posts\n  getBlogPosts: async (): Promise<BlogPost[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog`, {\n      next: { revalidate: 60 } // Cache for 1 minute\n    })\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog posts')\n    }\n    return response.json()\n  },\n\n  getBlogPost: async (slug: string): Promise<BlogPost> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/${slug}`, {\n      next: { revalidate: 60 } // Cache for 1 minute\n    })\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog post')\n    }\n    return response.json()\n  },\n\n\n\n  // Services\n  getServices: async (): Promise<Service[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/services`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch services')\n    }\n    return response.json()\n  },\n\n  // Tech Stack\n  getTechStack: async (): Promise<TechStack[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch tech stack')\n    }\n    return response.json()\n  },\n\n  // Testimonials\n  getTestimonials: async (): Promise<Testimonial[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch testimonials')\n    }\n    return response.json()\n  },\n\n  // Experiences\n  getExperiences: async (): Promise<Experience[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/experiences`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch experiences')\n    }\n    return response.json()\n  },\n\n  // Education\n  getEducation: async (): Promise<Education[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/education`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch education')\n    }\n    return response.json()\n  },\n\n  // Certifications\n  getCertifications: async (): Promise<Certification[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/certifications`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch certifications')\n    }\n    return response.json()\n  },\n}\n\n// Helper functions\nexport const getPublishedProjects = (projects: Project[]) =>\n  projects.filter(project => project.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedProjects = (projects: Project[]) =>\n  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)\n\nexport const getPublishedBlogPosts = (posts: BlogPost[]) =>\n  posts.filter(post => post.published).sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())\n\nexport const getFeaturedBlogPosts = (posts: BlogPost[]) =>\n  posts.filter(post => post.published && post.featured).sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())\n\nexport const getRelatedBlogPosts = (posts: BlogPost[], currentPost: BlogPost, limit: number = 3) => {\n  const publishedPosts = posts.filter(post => post.published && post.id !== currentPost.id);\n\n  // Find posts with similar tags or category\n  const relatedPosts = publishedPosts.filter(post =>\n    post.category === currentPost.category ||\n    post.tags.some(tag => currentPost.tags.includes(tag))\n  );\n\n  // If we have related posts, return up to limit\n  if (relatedPosts.length > 0) {\n    return relatedPosts.slice(0, limit);\n  }\n\n  // If no related posts found, return the most recent posts (excluding current)\n  return publishedPosts\n    .sort((a, b) => new Date(b.publishedAt || b.createdAt).getTime() - new Date(a.publishedAt || a.createdAt).getTime())\n    .slice(0, limit);\n}\n\nexport const getProjectsByCategory = (projects: Project[], category: string) => \n  category === 'All' \n    ? getPublishedProjects(projects)\n    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)\n\nexport const getPublishedServices = (services: Service[]) => \n  services.filter(service => service.published).sort((a, b) => a.order - b.order)\n\nexport const getTechStackByCategory = (techStack: TechStack[]) => {\n  const published = techStack.filter(tech => tech.published)\n  return published.reduce((acc, tech) => {\n    if (!acc[tech.category]) {\n      acc[tech.category] = []\n    }\n    acc[tech.category].push(tech)\n    return acc\n  }, {} as Record<string, TechStack[]>)\n}\n\nexport const getPublishedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedTestimonials = (testimonials: Testimonial[]) =>\n  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)\n\nexport const getPublishedExperiences = (experiences: Experience[]) =>\n  experiences.filter(experience => experience.published).sort((a, b) => a.order - b.order)\n\nexport const getPublishedEducation = (education: Education[]) =>\n  education.filter(edu => edu.published).sort((a, b) => a.order - b.order)\n\nexport const getPublishedCertifications = (certifications: Certification[]) =>\n  certifications.filter(cert => cert.published).sort((a, b) => a.order - b.order)\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;;;;;;;AACjC,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AA8HjD,MAAM,MAAM;IACjB,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,IAAI;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;YACvD,MAAM;gBAAE,YAAY;YAAG,EAAE,qBAAqB;QAChD;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,MAAM,EAAE;YAC/D,MAAM;gBAAE,YAAY;YAAG,EAAE,qBAAqB;QAChD;QACA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAIA,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC;QAC7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,iBAAiB;QACf,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,gBAAgB;QACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,gBAAgB,CAAC;QAC9D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC;QAC5D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,mBAAmB;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,CAAC;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,sBAAsB,CAAC,WAClC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7F,MAAM,wBAAwB,CAAC,QACpC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,OAAO;AAEhJ,MAAM,uBAAuB,CAAC,QACnC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,OAAO;AAEjK,MAAM,sBAAsB,CAAC,OAAmB,aAAuB,QAAgB,CAAC;IAC7F,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,YAAY,EAAE;IAExF,2CAA2C;IAC3C,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,OACzC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC;IAGlD,+CAA+C;IAC/C,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,OAAO,aAAa,KAAK,CAAC,GAAG;IAC/B;IAEA,8EAA8E;IAC9E,OAAO,eACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,OAAO,IAChH,KAAK,CAAC,GAAG;AACd;AAEO,MAAM,wBAAwB,CAAC,UAAqB,WACzD,aAAa,QACT,qBAAqB,YACrB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE9G,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;IACzD,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,MAAM,2BAA2B,CAAC,eACvC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAErF,MAAM,0BAA0B,CAAC,eACtC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7G,MAAM,0BAA0B,CAAC,cACtC,YAAY,MAAM,CAAC,CAAA,aAAc,WAAW,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAElF,MAAM,wBAAwB,CAAC,YACpC,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAElE,MAAM,6BAA6B,CAAC,iBACzC,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/experience-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, MapPin, ExternalLink, Briefcase, ArrowRight } from \"lucide-react\";\nimport { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';\nimport 'react-vertical-timeline-component/style.min.css';\nimport { Badge } from \"@/components/ui/badge\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { api, getPublishedExperiences, type Experience } from \"@/lib/api\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\n\n// Fallback experiences data\nconst fallbackExperiences = [\n  {\n    title: \"Senior Full Stack Developer\",\n    company: \"TechCorp Solutions\",\n    location: \"Mumbai, India\",\n    period: \"2022 - Present\",\n    type: \"Full-time\",\n    description: \"Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices.\",\n    achievements: [\n      \"Increased application performance by 40%\",\n      \"Led a team of 5 developers\",\n      \"Implemented CI/CD pipelines\" \n    ],\n    technologies: [\"React\", \"Next.js\", \"TypeScript\", \"Node.js\", \"PostgreSQL\", \"AWS\"],\n  },\n  {\n    title: \"Full Stack Developer\",\n    company: \"StartupXYZ\",\n    location: \"Remote\",\n    period: \"2021 - 2022\",\n    type: \"Full-time\",\n    description: \"Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect interfaces.\",\n    achievements: [\n      \"Built 10+ responsive web applications\",\n      \"Reduced page load times by 50%\",\n      \"Implemented real-time features\"\n    ],\n    technologies: [\"React\", \"Vue.js\", \"Express.js\", \"MongoDB\", \"Firebase\"],\n  },\n  {\n    title: \"Frontend Developer\",\n    company: \"Digital Agency Pro\",\n    location: \"Mumbai, India\",\n    period: \"2020 - 2021\",\n    type: \"Full-time\",\n    description: \"Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.\",\n    achievements: [\n      \"Delivered 20+ client projects on time\",\n      \"Improved client satisfaction by 25%\",\n      \"Created reusable component library\"\n    ],\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"React\", \"Sass\", \"Figma\"],\n  }\n];\n\nexport function ExperienceSection() {\n  const [experiences, setExperiences] = useState<Experience[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  useEffect(() => {\n    const fetchExperiences = async () => {\n      try {\n        const data = await api.getExperiences();\n        console.log('Fetched experiences data:', data); // Debug log\n        const publishedExperiences = getPublishedExperiences(data);\n        console.log('Published experiences:', publishedExperiences); // Debug log\n        // Show only the first 3 experiences for the home page\n        const limitedExperiences = publishedExperiences.slice(0, 3);\n        console.log('Limited experiences for home page:', limitedExperiences); // Debug log\n        setExperiences(limitedExperiences);\n      } catch (error) {\n        console.error('Failed to fetch experiences:', error);\n        // Use fallback data if API fails\n        setExperiences(fallbackExperiences.map((exp, index) => ({\n          id: `fallback-${index}`,\n          title: exp.title,\n          company: exp.company,\n          companyLogo: undefined,\n          location: exp.location,\n          period: exp.period,\n          type: exp.type,\n          description: exp.description,\n          achievements: exp.achievements,\n          technologies: exp.technologies,\n          website: undefined,\n          order: index,\n          published: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        })));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchExperiences();\n  }, []);\n\n  if (loading) {\n    return (\n      <section className=\"py-20 bg-muted/30\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"animate-pulse space-y-8\">\n            <div className=\"text-center\">\n              <div className=\"h-12 bg-gray-300 rounded w-64 mx-auto mb-4\"></div>\n              <div className=\"h-6 bg-gray-300 rounded w-96 mx-auto\"></div>\n            </div>\n            <div className=\"max-w-4xl mx-auto space-y-8\">\n              {[1, 2, 3].map((i) => (\n                <div key={i} className=\"flex items-start space-x-4\">\n                  <div className=\"w-16 h-16 bg-gray-300 rounded-full flex-shrink-0\"></div>\n                  <div className=\"flex-1 space-y-3\">\n                    <div className=\"h-6 bg-gray-300 rounded w-3/4\"></div>\n                    <div className=\"h-4 bg-gray-300 rounded w-1/2\"></div>\n                    <div className=\"h-20 bg-gray-300 rounded\"></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Work Experience</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            My professional journey in web development, building scalable applications and leading development teams.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={inView ? { opacity: 1 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          <VerticalTimeline>\n            {experiences.map((experience) => {\n              return (\n                <VerticalTimelineElement\n                  key={experience.id}\n                  className=\"vertical-timeline-element--work\"\n                  contentStyle={{\n                    background: 'hsl(var(--card))',\n                    color: 'hsl(var(--card-foreground))',\n                    border: '1px solid hsl(var(--border))',\n                    borderRadius: '12px',\n                    boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',\n                  }}\n                  contentArrowStyle={{\n                    borderRight: '7px solid hsl(var(--border))',\n                  }}\n                  date={experience.period}\n                  iconStyle={{\n                    background: '#ffffff',\n                    color: '#000',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    border: '3px solid hsl(var(--background))',\n                    boxShadow: '0 0 0 2px hsl(var(--border))',\n                    width: '60px',\n                    height: '60px',\n                  }}\n                  icon={\n                    experience.companyLogo ? (\n                      <Image\n                        src={experience.companyLogo}\n                        alt={`${experience.company} logo`}\n                        width={40}\n                        height={40}\n                        className=\"rounded-full object-contain\"\n                        onError={(e) => {\n                          console.error('Image failed to load:', experience.companyLogo, e);\n                        }}\n                        onLoad={() => {\n                          console.log('Image loaded successfully:', experience.companyLogo);\n                        }}\n                      />\n                    ) : (\n                      <Briefcase className=\"h-6 w-6\" />\n                    )\n                  }\n                >\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h3 className=\"text-xl font-bold\">{experience.title}</h3>\n                      <h4 className=\"text-lg font-semibold text-primary\">{experience.company}</h4>\n                      <div className=\"flex items-center space-x-4 text-sm text-muted-foreground mt-2\">\n                        <div className=\"flex items-center\">\n                          <MapPin className=\"h-4 w-4 mr-1\" />\n                          {experience.location}\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"h-4 w-4 mr-1\" />\n                          {experience.period}\n                        </div>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {experience.type}\n                        </Badge>\n                      </div>\n                    </div>\n\n                    <p className=\"text-muted-foreground\">{experience.description}</p>\n\n                    <div>\n                      <h4 className=\"font-semibold mb-2\">Key Achievements:</h4>\n                      <ul className=\"space-y-1\">\n                        {experience.achievements.slice(0, 3).map((achievement, achievementIndex) => (\n                          <li key={achievementIndex} className=\"flex items-start space-x-2 text-sm text-muted-foreground\">\n                            <div className=\"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0\" />\n                            <span>{achievement}</span>\n                          </li>\n                        ))}\n                        {experience.achievements.length > 3 && (\n                          <li className=\"text-sm text-muted-foreground ml-3.5\">\n                            +{experience.achievements.length - 3} more achievements\n                          </li>\n                        )}\n                      </ul>\n                    </div>\n\n                    <div>\n                      <h4 className=\"font-semibold mb-2\">Technologies:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {experience.technologies.slice(0, 6).map((tech) => (\n                          <Badge key={tech} variant=\"secondary\" className=\"text-xs\">\n                            {tech}\n                          </Badge>\n                        ))}\n                        {experience.technologies.length > 6 && (\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            +{experience.technologies.length - 6} more\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n\n                    {experience.website && (\n                      <div className=\"pt-2\">\n                        <a\n                          href={experience.website}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"inline-flex items-center text-sm text-primary hover:underline\"\n                        >\n                          <ExternalLink className=\"h-4 w-4 mr-1\" />\n                          Visit Company Website\n                        </a>\n                      </div>\n                    )}\n                  </div>\n                </VerticalTimelineElement>\n              );\n            })}\n          </VerticalTimeline>\n        </motion.div>\n\n        <motion.div\n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n        >\n          <Link href=\"/about#experience\">\n            <Button variant=\"outline\" size=\"lg\" className=\"group\">\n              View Full Experience\n              <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n            </Button>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAcA,4BAA4B;AAC5B,MAAM,sBAAsB;IAC1B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;SAAM;IAClF;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAU;YAAc;YAAW;SAAW;IACxE;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAO;YAAc;YAAS;YAAQ;SAAQ;IACvE;CACD;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,MAAM,OAAO,MAAM,iHAAA,CAAA,MAAG,CAAC,cAAc;gBACrC,QAAQ,GAAG,CAAC,6BAA6B,OAAO,YAAY;gBAC5D,MAAM,uBAAuB,CAAA,GAAA,iHAAA,CAAA,0BAAuB,AAAD,EAAE;gBACrD,QAAQ,GAAG,CAAC,0BAA0B,uBAAuB,YAAY;gBACzE,sDAAsD;gBACtD,MAAM,qBAAqB,qBAAqB,KAAK,CAAC,GAAG;gBACzD,QAAQ,GAAG,CAAC,sCAAsC,qBAAqB,YAAY;gBACnF,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,iCAAiC;gBACjC,eAAe,oBAAoB,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;wBACtD,IAAI,CAAC,SAAS,EAAE,OAAO;wBACvB,OAAO,IAAI,KAAK;wBAChB,SAAS,IAAI,OAAO;wBACpB,aAAa;wBACb,UAAU,IAAI,QAAQ;wBACtB,QAAQ,IAAI,MAAM;wBAClB,MAAM,IAAI,IAAI;wBACd,aAAa,IAAI,WAAW;wBAC5B,cAAc,IAAI,YAAY;wBAC9B,cAAc,IAAI,YAAY;wBAC9B,SAAS;wBACT,OAAO;wBACP,WAAW;wBACX,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;oBACnC,CAAC;YACH,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;oCAAY,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCALT;;;;;;;;;;;;;;;;;;;;;;;;;;IAcxB;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,SAAS;wBAAE,SAAS;oBAAE,IAAI,CAAC;oBACpC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,kLAAA,CAAA,mBAAgB;kCACd,YAAY,GAAG,CAAC,CAAC;4BAChB,qBACE,8OAAC,kLAAA,CAAA,0BAAuB;gCAEtB,WAAU;gCACV,cAAc;oCACZ,YAAY;oCACZ,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,WAAW;gCACb;gCACA,mBAAmB;oCACjB,aAAa;gCACf;gCACA,MAAM,WAAW,MAAM;gCACvB,WAAW;oCACT,YAAY;oCACZ,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,gBAAgB;oCAChB,QAAQ;oCACR,WAAW;oCACX,OAAO;oCACP,QAAQ;gCACV;gCACA,MACE,WAAW,WAAW,iBACpB,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,WAAW,WAAW;oCAC3B,KAAK,GAAG,WAAW,OAAO,CAAC,KAAK,CAAC;oCACjC,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,SAAS,CAAC;wCACR,QAAQ,KAAK,CAAC,yBAAyB,WAAW,WAAW,EAAE;oCACjE;oCACA,QAAQ;wCACN,QAAQ,GAAG,CAAC,8BAA8B,WAAW,WAAW;oCAClE;;;;;2DAGF,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;0CAIzB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB,WAAW,KAAK;;;;;;8DACnD,8OAAC;oDAAG,WAAU;8DAAsC,WAAW,OAAO;;;;;;8DACtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,WAAW,QAAQ;;;;;;;sEAEtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,WAAW,MAAM;;;;;;;sEAEpB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,WAAW,IAAI;;;;;;;;;;;;;;;;;;sDAKtB,8OAAC;4CAAE,WAAU;sDAAyB,WAAW,WAAW;;;;;;sDAE5D,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAG,WAAU;;wDACX,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,aAAa,iCACrD,8OAAC;gEAA0B,WAAU;;kFACnC,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;kFAAM;;;;;;;+DAFA;;;;;wDAKV,WAAW,YAAY,CAAC,MAAM,GAAG,mBAChC,8OAAC;4DAAG,WAAU;;gEAAuC;gEACjD,WAAW,YAAY,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;wDACZ,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACxC,8OAAC,iIAAA,CAAA,QAAK;gEAAY,SAAQ;gEAAY,WAAU;0EAC7C;+DADS;;;;;wDAIb,WAAW,YAAY,CAAC,MAAM,GAAG,mBAChC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,WAAW,YAAY,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;;;;;;;wCAM5C,WAAW,OAAO,kBACjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAM,WAAW,OAAO;gDACxB,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;+BA1G5C,WAAW,EAAE;;;;;wBAkHxB;;;;;;;;;;;8BAIJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;;gCAAQ;8CAEpD,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/tech-stack.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport Image from \"next/image\";\nimport { Loader2 } from \"lucide-react\";\nimport { useState, useEffect } from \"react\";\nimport { api, getTechStackByCategory, type TechStack } from \"@/lib/api\";\n\ninterface TechItem {\n  id: string;\n  name: string;\n  logo: string;\n  color: string;\n  category: string;\n}\n\nconst TechRow = ({\n  techs,\n  direction = \"left\",\n  speed = 30\n}: {\n  techs: TechItem[];\n  direction?: \"left\" | \"right\";\n  speed?: number;\n}) => {\n  // Create multiple copies for seamless infinite scroll\n  const duplicatedTechs = [...techs, ...techs, ...techs];\n\n  return (\n    <div className=\"flex overflow-hidden mask-gradient\">\n      <motion.div\n        className=\"flex space-x-6 whitespace-nowrap\"\n        animate={{\n          x: direction === \"left\" ? [0, -100 * techs.length] : [-100 * techs.length, 0],\n        }}\n        transition={{\n          x: {\n            repeat: Infinity,\n            repeatType: \"loop\",\n            duration: speed,\n            ease: \"linear\",\n          },\n        }}\n      >\n        {duplicatedTechs.map((tech, index) => (\n          <motion.div\n            key={`${tech.id}-${index}`}\n            className=\"flex items-center space-x-3 bg-card border border-border rounded-lg px-6 py-3 shadow-sm hover:shadow-md transition-all duration-200 min-w-fit\"\n            whileHover={{ scale: 1.05, y: -2 }}\n          >\n            <div className=\"w-8 h-8 relative flex-shrink-0\">\n              <Image\n                src={tech.logo}\n                alt={tech.name}\n                fill\n                className=\"object-contain\"\n              />\n            </div>\n            <span className={`font-medium ${tech.color} text-sm`}>{tech.name}</span>\n          </motion.div>\n        ))}\n      </motion.div>\n    </div>\n  );\n};\n\nexport function TechStack() {\n  const [techStackByCategory, setTechStackByCategory] = useState<Record<string, TechStack[]>>({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Fetch tech stack from CMS\n  useEffect(() => {\n    const fetchTechStack = async () => {\n      try {\n        setIsLoading(true);\n        const data = await api.getTechStack();\n        setTechStackByCategory(getTechStackByCategory(data));\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to fetch tech stack');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTechStack();\n  }, []);\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-muted/30\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">Technologies I Love</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Loading technologies...\n            </p>\n          </div>\n          <div className=\"flex justify-center\">\n            <Loader2 className=\"h-8 w-8 animate-spin\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <section className=\"py-20 bg-muted/30\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">Technologies I Love</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Unable to load technologies. Please try again later.\n            </p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Technologies I Love</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            I&apos;m passionate about cutting-edge technologies and constantly learning new tools\n            to build amazing digital experiences.\n          </p>\n        </motion.div>\n\n        <motion.div\n          className=\"space-y-12\"\n          initial={{ opacity: 0 }}\n          animate={inView ? { opacity: 1 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          {/* Frontend Technologies */}\n          {techStackByCategory.frontend && (\n            <div className=\"space-y-6\">\n              <motion.h3\n                className=\"text-2xl font-bold text-center\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: 0.3 }}\n              >\n                <span className=\"gradient-text\">Frontend</span>\n              </motion.h3>\n              <TechRow\n                techs={techStackByCategory.frontend}\n                direction=\"left\"\n                speed={20}\n              />\n            </div>\n          )}\n\n          {/* Backend Technologies */}\n          {techStackByCategory.backend && (\n            <div className=\"space-y-6\">\n              <motion.h3\n                className=\"text-2xl font-bold text-center\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: 0.4 }}\n              >\n                <span className=\"gradient-text\">Backend</span>\n              </motion.h3>\n              <TechRow\n                techs={techStackByCategory.backend}\n                direction=\"right\"\n                speed={25}\n              />\n            </div>\n          )}\n\n          {/* Tools & Others */}\n          {(techStackByCategory.tools || techStackByCategory.database || techStackByCategory.cloud) && (\n            <div className=\"space-y-6\">\n              <motion.h3\n                className=\"text-2xl font-bold text-center\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: 0.5 }}\n              >\n                <span className=\"gradient-text\">Tools & Infrastructure</span>\n              </motion.h3>\n              <TechRow\n                techs={[\n                  ...(techStackByCategory.tools || []),\n                  ...(techStackByCategory.database || []),\n                  ...(techStackByCategory.cloud || [])\n                ]}\n                direction=\"left\"\n                speed={30}\n              />\n            </div>\n          )}\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mt-20\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          {[\n            { number: \"3+\", label: \"Years Experience\" },\n            { number: \"50+\", label: \"Projects Completed\" },\n            { number: \"20+\", label: \"Happy Clients\" },\n            { number: \"99%\", label: \"Client Satisfaction\" },\n          ].map((stat, index) => (\n            <motion.div\n              key={stat.label}\n              className=\"text-center\"\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={inView ? { opacity: 1, scale: 1 } : {}}\n              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n            >\n              <div className=\"text-3xl sm:text-4xl font-bold gradient-text-blue mb-2\">\n                {stat.number}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAiBA,MAAM,UAAU,CAAC,EACf,KAAK,EACL,YAAY,MAAM,EAClB,QAAQ,EAAE,EAKX;IACC,sDAAsD;IACtD,MAAM,kBAAkB;WAAI;WAAU;WAAU;KAAM;IAEtD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBACP,GAAG,cAAc,SAAS;oBAAC;oBAAG,CAAC,MAAM,MAAM,MAAM;iBAAC,GAAG;oBAAC,CAAC,MAAM,MAAM,MAAM;oBAAE;iBAAE;YAC/E;YACA,YAAY;gBACV,GAAG;oBACD,QAAQ;oBACR,YAAY;oBACZ,UAAU;oBACV,MAAM;gBACR;YACF;sBAEC,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,YAAY;wBAAE,OAAO;wBAAM,GAAG,CAAC;oBAAE;;sCAEjC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,IAAI;gCACd,KAAK,KAAK,IAAI;gCACd,IAAI;gCACJ,WAAU;;;;;;;;;;;sCAGd,8OAAC;4BAAK,WAAW,CAAC,YAAY,EAAE,KAAK,KAAK,CAAC,QAAQ,CAAC;sCAAG,KAAK,IAAI;;;;;;;mBAZ3D,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;AAkBtC;AAEO,SAAS;IACd,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B,CAAC;IAC7F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,aAAa;gBACb,MAAM,OAAO,MAAM,iHAAA,CAAA,MAAG,CAAC,YAAY;gBACnC,uBAAuB,CAAA,GAAA,iHAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC9C,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;IAK7B;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,SAAS;wBAAE,SAAS;oBAAE,IAAI,CAAC;oBACpC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;wBAGvC,oBAAoB,QAAQ,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,8OAAC;oCACC,OAAO,oBAAoB,QAAQ;oCACnC,WAAU;oCACV,OAAO;;;;;;;;;;;;wBAMZ,oBAAoB,OAAO,kBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,8OAAC;oCACC,OAAO,oBAAoB,OAAO;oCAClC,WAAU;oCACV,OAAO;;;;;;;;;;;;wBAMZ,CAAC,oBAAoB,KAAK,IAAI,oBAAoB,QAAQ,IAAI,oBAAoB,KAAK,mBACtF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;8CAElC,8OAAC;oCACC,OAAO;2CACD,oBAAoB,KAAK,IAAI,EAAE;2CAC/B,oBAAoB,QAAQ,IAAI,EAAE;2CAClC,oBAAoB,KAAK,IAAI,EAAE;qCACpC;oCACD,WAAU;oCACV,OAAO;;;;;;;;;;;;;;;;;;8BAOf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC;wBACC;4BAAE,QAAQ;4BAAM,OAAO;wBAAmB;wBAC1C;4BAAE,QAAQ;4BAAO,OAAO;wBAAqB;wBAC7C;4BAAE,QAAQ;4BAAO,OAAO;wBAAgB;wBACxC;4BAAE,QAAQ;4BAAO,OAAO;wBAAsB;qBAC/C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI,CAAC;4BAC9C,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;;8CAEtD,8OAAC;oCAAI,WAAU;8CACZ,KAAK,MAAM;;;;;;8CAEd,8OAAC;oCAAI,WAAU;8CAAiC,KAAK,KAAK;;;;;;;2BATrD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAgB7B", "debugId": null}}, {"offset": {"line": 3113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/services.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport {\n  Code,\n  Palette,\n  Smartphone,\n  Database,\n  Cloud,\n  Zap,\n  ArrowRight,\n  Loader2\n} from \"lucide-react\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { useState, useEffect } from \"react\";\nimport { api, getPublishedServices, type Service } from \"@/lib/api\";\n\n// Icon mapping for services\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Code,\n  Palette,\n  Smartphone,\n  Database,\n  Cloud,\n  Zap,\n};\n\nexport function Services() {\n  const [services, setServices] = useState<Service[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Fetch services from CMS\n  useEffect(() => {\n    const fetchServices = async () => {\n      try {\n        setIsLoading(true);\n        const data = await api.getServices();\n        setServices(getPublishedServices(data));\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to fetch services');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchServices();\n  }, []);\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold mb-4\">Services</h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Loading services...\n            </p>\n          </div>\n          <div className=\"flex justify-center\">\n            <Loader2 className=\"h-8 w-8 animate-spin\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold mb-4\">Services</h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Unable to load services. Please try again later.\n            </p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Services I Offer</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            From concept to deployment, I provide comprehensive development services \n            to bring your digital vision to life.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => {\n            const Icon = iconMap[service.icon] || Code; // Fallback to Code icon\n            return (\n              <motion.div\n                key={service.id}\n                initial={{ opacity: 0, y: 50 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.8, delay: index * 0.1 }}\n              >\n                <Card className=\"h-full hover-lift group cursor-pointer border-border/50 hover:border-border transition-all duration-300\">\n                  <CardHeader>\n                    <div className={`w-12 h-12 rounded-lg ${service.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className={`h-6 w-6 ${service.color}`} />\n                    </div>\n                    <CardTitle className=\"text-xl font-bold group-hover:text-primary transition-colors duration-300\">\n                      {service.title}\n                    </CardTitle>\n                    <CardDescription className=\"text-muted-foreground\">\n                      {service.description}\n                    </CardDescription>\n                  </CardHeader>\n                  <CardContent>\n                    <ul className=\"space-y-2 mb-6\">\n                      {service.features.map((feature, featureIndex) => (\n                        <li key={featureIndex} className=\"flex items-center text-sm text-muted-foreground\">\n                          <div className={`w-1.5 h-1.5 rounded-full ${service.color.replace('text-', 'bg-')} mr-3`} />\n                          {feature}\n                        </li>\n                      ))}\n                    </ul>\n                    <Button\n                      variant=\"ghost\"\n                      className=\"w-full group-hover:bg-accent transition-colors duration-300\"\n                    >\n                      Learn More\n                      <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            );\n          })}\n        </div>\n\n        {/* CTA Section */}\n        <motion.div\n          className=\"text-center mt-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n        >\n          <div className=\"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to Start Your Project?</h3>\n            <p className=\"text-muted-foreground mb-6 max-w-2xl mx-auto\">\n              Let&apos;s discuss how I can help bring your ideas to life with modern,\n              scalable, and user-friendly solutions.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"group\">\n                Get Started\n                <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                View Portfolio\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AAjBA;;;;;;;;;AAmBA,4BAA4B;AAC5B,MAAM,UAAuE;IAC3E,MAAA,kMAAA,CAAA,OAAI;IACJ,SAAA,wMAAA,CAAA,UAAO;IACP,YAAA,8MAAA,CAAA,aAAU;IACV,UAAA,0MAAA,CAAA,WAAQ;IACR,OAAA,oMAAA,CAAA,QAAK;IACL,KAAA,gMAAA,CAAA,MAAG;AACL;AAEO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,aAAa;gBACb,MAAM,OAAO,MAAM,iHAAA,CAAA,MAAG,CAAC,WAAW;gBAClC,YAAY,CAAA,GAAA,iHAAA,CAAA,uBAAoB,AAAD,EAAE;gBACjC,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;IAK7B;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,OAAO,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,kMAAA,CAAA,OAAI,EAAE,wBAAwB;wBACpE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,QAAQ,OAAO,CAAC,8FAA8F,CAAC;0DACrJ,cAAA,8OAAC;oDAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;0DAE7C,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,QAAQ,KAAK;;;;;;0DAEhB,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;kDAGxB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAI,WAAW,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC,OAAO,CAAC,SAAS,OAAO,KAAK,CAAC;;;;;;4DACvF;;uDAFM;;;;;;;;;;0DAMb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2BA/BvB,QAAQ,EAAE;;;;;oBAqCrB;;;;;;8BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;4CAAQ;0DAElC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}, {"offset": {"line": 3642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/projects-showcase.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo } from \"react\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { ExternalLink, ArrowRight, Filter, Loader2, Code2 } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { useEffect } from \"react\";\nimport { api, getFeaturedProjects, getProjectsByCategory, type Project } from \"@/lib/api\";\n\n// Fallback projects data\nconst fallbackProjects: Project[] = [\n  {\n    id: \"fallback-1\",\n    title: \"E-Commerce Platform\",\n    description: \"A modern e-commerce platform built with Next.js, featuring real-time inventory management, secure payments, and responsive design.\",\n    longDescription: \"A comprehensive e-commerce solution with advanced features including product catalog, shopping cart, user authentication, payment processing, and admin dashboard.\",\n    image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop\",\n    category: \"Web Development\",\n    technologies: [\"Next.js\", \"TypeScript\", \"Stripe\", \"PostgreSQL\", \"Tailwind CSS\"],\n    liveUrl: \"https://example-ecommerce.vercel.app\",\n    githubUrl: \"https://github.com/ashishkamat/ecommerce-platform\",\n    featured: true,\n    published: true,\n    order: 1,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: \"fallback-2\",\n    title: \"Task Management App\",\n    description: \"A collaborative task management application with real-time updates, team collaboration features, and intuitive drag-and-drop interface.\",\n    longDescription: \"A full-featured project management tool with kanban boards, team collaboration, real-time notifications, and comprehensive reporting.\",\n    image: \"https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop\",\n    category: \"Web Development\",\n    technologies: [\"React\", \"Node.js\", \"Socket.io\", \"MongoDB\", \"Material-UI\"],\n    liveUrl: \"https://taskmanager-demo.vercel.app\",\n    githubUrl: \"https://github.com/ashishkamat/task-manager\",\n    featured: true,\n    published: true,\n    order: 2,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: \"fallback-3\",\n    title: \"Weather Dashboard\",\n    description: \"A beautiful weather dashboard with location-based forecasts, interactive maps, and detailed weather analytics.\",\n    image: \"https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&h=600&fit=crop\",\n    category: \"Web Development\",\n    technologies: [\"Vue.js\", \"OpenWeather API\", \"Chart.js\", \"CSS3\"],\n    liveUrl: \"https://weather-dashboard-demo.vercel.app\",\n    githubUrl: \"https://github.com/ashishkamat/weather-dashboard\",\n    featured: false,\n    published: true,\n    order: 3,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: \"fallback-4\",\n    title: \"Portfolio Website\",\n    description: \"A responsive portfolio website showcasing modern design principles and smooth animations.\",\n    image: \"https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=800&h=600&fit=crop\",\n    category: \"UI/UX Design\",\n    technologies: [\"Next.js\", \"Framer Motion\", \"Tailwind CSS\", \"TypeScript\"],\n    liveUrl: \"https://portfolio-demo.vercel.app\",\n    githubUrl: \"https://github.com/ashishkamat/portfolio\",\n    featured: false,\n    published: true,\n    order: 4,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: \"fallback-5\",\n    title: \"Blog Platform\",\n    description: \"A modern blog platform with markdown support, SEO optimization, and content management features.\",\n    image: \"https://images.unsplash.com/photo-1486312338219-ce68e2c6b7d3?w=800&h=600&fit=crop\",\n    category: \"Web Development\",\n    technologies: [\"Next.js\", \"MDX\", \"Prisma\", \"PostgreSQL\", \"Vercel\"],\n    liveUrl: \"https://blog-platform-demo.vercel.app\",\n    githubUrl: \"https://github.com/ashishkamat/blog-platform\",\n    featured: false,\n    published: true,\n    order: 5,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\nexport function ProjectsShowcase() {\n  const [selectedCategory, setSelectedCategory] = useState(\"All\");\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Fetch projects from CMS\n  useEffect(() => {\n    const fetchProjects = async () => {\n      try {\n        setIsLoading(true);\n        const data = await api.getProjects();\n        setProjects(data);\n        setError(null);\n      } catch (err) {\n        console.error('Failed to fetch projects:', err);\n        // Use fallback data if API fails\n        setProjects(fallbackProjects);\n        setError(null); // Don't show error when we have fallback data\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchProjects();\n  }, []);\n\n  // Memoized computed values\n  const categories = useMemo((): string[] => {\n    if (!projects.length) return [\"All\"];\n    const uniqueCategories = Array.from(new Set(projects.map((p: Project) => p.category as string)));\n    return [\"All\", ...uniqueCategories.sort()];\n  }, [projects]);\n\n  const filteredProjects = useMemo(() =>\n    getProjectsByCategory(projects, selectedCategory),\n    [projects, selectedCategory]\n  );\n\n  const featuredProjects = useMemo(() =>\n    getFeaturedProjects(projects),\n    [projects]\n  );\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold mb-4\">Featured Projects</h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Loading my latest work...\n            </p>\n          </div>\n          <div className=\"flex justify-center\">\n            <Loader2 className=\"h-8 w-8 animate-spin\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold mb-4\">Featured Projects</h2>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Unable to load projects. Please try again later.\n            </p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Featured Projects</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A showcase of my recent work, featuring modern web applications \n            built with cutting-edge technologies.\n          </p>\n        </motion.div>\n\n        {/* Featured Projects */}\n        <div className=\"grid md:grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-12 lg:mb-16\">\n          {featuredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.2 }}\n            >\n              <Card className=\"overflow-hidden hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                <div className=\"relative h-48 sm:h-56 lg:h-64 overflow-hidden\">\n                  {project.image ? (\n                    <Image\n                      src={project.image}\n                      alt={project.title}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <>\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20\" />\n                      <div className=\"absolute inset-0 flex items-center justify-center\">\n                        <div className=\"text-4xl sm:text-5xl lg:text-6xl opacity-20\">🚀</div>\n                      </div>\n                    </>\n                  )}\n                  <div className=\"absolute top-3 right-3 sm:top-4 sm:right-4\">\n                    <Badge variant=\"secondary\" className=\"bg-background/80 backdrop-blur-sm text-xs\">\n                      {project.category}\n                    </Badge>\n                  </div>\n                </div>\n                <CardContent className=\"p-4 sm:p-6\">\n                  <h3 className=\"text-lg sm:text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-sm sm:text-base text-muted-foreground mb-4 line-clamp-2\">\n                    {project.description}\n                  </p>\n                  <div className=\"flex flex-wrap gap-1.5 sm:gap-2 mb-4\">\n                    {project.technologies.slice(0, 3).map((tech) => (\n                      <Badge key={tech} variant=\"outline\" className=\"text-xs\">\n                        {tech}\n                      </Badge>\n                    ))}\n                    {project.technologies.length > 3 && (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        +{project.technologies.length - 3} more\n                      </Badge>\n                    )}\n                  </div>\n                  <div className=\"flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0\">\n                    {project.liveUrl && (\n                      <Button size=\"sm\" className=\"flex-1 group\" asChild>\n                        <Link href={project.liveUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          <ExternalLink className=\"mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300\" />\n                          Live Demo\n                        </Link>\n                      </Button>\n                    )}\n                    {project.githubUrl && (\n                      <Button variant=\"outline\" size=\"sm\" className=\"sm:w-auto\" asChild>\n                        <Link href={project.githubUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          <Code2 className=\"h-4 w-4\" />\n                        </Link>\n                      </Button>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Category Filter */}\n        <motion.div\n          className=\"flex flex-wrap justify-center gap-2 mb-8 sm:mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          <Filter className=\"h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground mr-2 mt-2\" />\n          {categories.map((category: string) => (\n            <Button\n              key={category}\n              variant={selectedCategory === category ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => setSelectedCategory(category)}\n              className=\"transition-all duration-300 text-xs sm:text-sm\"\n            >\n              {category}\n            </Button>\n          ))}\n        </motion.div>\n\n        {/* All Projects Grid */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6\">\n          {filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, scale: 0.9 }}\n              animate={inView ? { opacity: 1, scale: 1 } : {}}\n              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}\n              layout\n            >\n              <Card className=\"overflow-hidden hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                <div className=\"relative h-48 overflow-hidden\">\n                  {project.image ? (\n                    <Image\n                      src={project.image}\n                      alt={project.title}\n                      fill\n                      className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  ) : (\n                    <>\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10\" />\n                      <div className=\"absolute inset-0 flex items-center justify-center\">\n                        <div className=\"text-4xl opacity-20\">💻</div>\n                      </div>\n                    </>\n                  )}\n                  <div className=\"absolute top-3 right-3\">\n                    <Badge variant=\"secondary\" className=\"text-xs bg-background/80 backdrop-blur-sm\">\n                      {project.category}\n                    </Badge>\n                  </div>\n                </div>\n                <CardContent className=\"p-4\">\n                  <h3 className=\"font-bold mb-2 group-hover:text-primary transition-colors duration-300\">\n                    {project.title}\n                  </h3>\n                  <p className=\"text-sm text-muted-foreground mb-3 line-clamp-2\">\n                    {project.description}\n                  </p>\n                  <div className=\"flex flex-wrap gap-1 mb-3\">\n                    {project.technologies.slice(0, 2).map((tech) => (\n                      <Badge key={tech} variant=\"outline\" className=\"text-xs\">\n                        {tech}\n                      </Badge>\n                    ))}\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    {project.liveUrl && (\n                      <Button size=\"sm\" variant=\"outline\" className=\"flex-1 text-xs\" asChild>\n                        <Link href={project.liveUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          <ExternalLink className=\"mr-1 h-3 w-3\" />\n                          Demo\n                        </Link>\n                      </Button>\n                    )}\n                    {project.githubUrl && (\n                      <Button variant=\"outline\" size=\"sm\" asChild>\n                        <Link href={project.githubUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          <Code2 className=\"h-3 w-3\" />\n                        </Link>\n                      </Button>\n                    )}\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* View All Projects CTA */}\n        <motion.div\n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.8 }}\n        >\n          <Button asChild size=\"lg\" className=\"group\">\n            <Link href=\"/projects\">\n              View All Projects\n              <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAZA;;;;;;;;;;;;;AAcA,yBAAyB;AACzB,MAAM,mBAA8B;IAClC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAc;YAAU;YAAc;SAAe;QAC/E,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAS;YAAW;YAAa;YAAW;SAAc;QACzE,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAU;YAAmB;YAAY;SAAO;QAC/D,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAiB;YAAgB;SAAa;QACxE,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,cAAc;YAAC;YAAW;YAAO;YAAU;YAAc;SAAS;QAClE,SAAS;QACT,WAAW;QACX,UAAU;QACV,WAAW;QACX,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,aAAa;gBACb,MAAM,OAAO,MAAM,iHAAA,CAAA,MAAG,CAAC,WAAW;gBAClC,YAAY;gBACZ,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,iCAAiC;gBACjC,YAAY;gBACZ,SAAS,OAAO,8CAA8C;YAChE,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI,CAAC,SAAS,MAAM,EAAE,OAAO;YAAC;SAAM;QACpC,MAAM,mBAAmB,MAAM,IAAI,CAAC,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAe,EAAE,QAAQ;QACnF,OAAO;YAAC;eAAU,iBAAiB,IAAI;SAAG;IAC5C,GAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAC/B,CAAA,GAAA,iHAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU,mBAChC;QAAC;QAAU;KAAiB;IAG9B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAC/B,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE,WACpB;QAAC;KAAS;IAGZ,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;IAK7B;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAOjE,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,KAAK,iBACZ,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,KAAK;gDAClB,IAAI;gDACJ,WAAU;;;;;qEAGZ;;kEACE,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAA8C;;;;;;;;;;;;;0DAInE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;kDAIvB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,iIAAA,CAAA,QAAK;4DAAY,SAAQ;4DAAU,WAAU;sEAC3C;2DADS;;;;;oDAIb,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAAU;4DACzC,QAAQ,YAAY,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,OAAO,kBACd,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;wDAAe,OAAO;kEAChD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,QAAQ,OAAO;4DAAE,QAAO;4DAAS,KAAI;;8EAC/C,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAyE;;;;;;;;;;;;oDAKtG,QAAQ,SAAS,kBAChB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;wDAAY,OAAO;kEAC/D,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,QAAQ,SAAS;4DAAE,QAAO;4DAAS,KAAI;sEACjD,cAAA,8OAAC,0MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA3DxB,QAAQ,EAAE;;;;;;;;;;8BAuErB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACjB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,qBAAqB,WAAW,YAAY;gCACrD,MAAK;gCACL,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET;+BANI;;;;;;;;;;;8BAYX,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI,CAAC;4BAC9C,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;4BACtD,MAAM;sCAEN,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,KAAK,iBACZ,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,KAAK;gDAClB,IAAI;gDACJ,WAAU;;;;;qEAGZ;;kEACE,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAsB;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;kDAIvB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC,iIAAA,CAAA,QAAK;wDAAY,SAAQ;wDAAU,WAAU;kEAC3C;uDADS;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,OAAO,kBACd,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAU,WAAU;wDAAiB,OAAO;kEACpE,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,QAAQ,OAAO;4DAAE,QAAO;4DAAS,KAAI;;8EAC/C,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;oDAK9C,QAAQ,SAAS,kBAChB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,OAAO;kEACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,QAAQ,SAAS;4DAAE,QAAO;4DAAS,KAAI;sEACjD,cAAA,8OAAC,0MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAvDxB,QAAQ,EAAE;;;;;;;;;;8BAmErB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,WAAU;kCAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAY;8CAErB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 4492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/testimonials.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { ChevronLeft, ChevronRight, Star, Quote, Loader2 } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { api, getPublishedTestimonials, type Testimonial } from \"@/lib/api\";\n\nexport function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Fetch testimonials from CMS\n  useEffect(() => {\n    const fetchTestimonials = async () => {\n      try {\n        setIsLoading(true);\n        const data = await api.getTestimonials();\n        setTestimonials(getPublishedTestimonials(data));\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to fetch testimonials');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTestimonials();\n  }, []);\n\n  const nextTestimonial = useCallback(() => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  }, [testimonials.length]);\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n  };\n\n  // Auto-advance testimonials\n  useEffect(() => {\n    if (testimonials.length === 0) return;\n    const interval = setInterval(nextTestimonial, 5000);\n    return () => clearInterval(interval);\n  }, [nextTestimonial, testimonials.length]);\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">What Clients Say</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Loading testimonials...\n            </p>\n          </div>\n          <div className=\"flex justify-center\">\n            <Loader2 className=\"h-8 w-8 animate-spin\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">What Clients Say</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Unable to load testimonials. Please try again later.\n            </p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  // No testimonials state\n  if (testimonials.length === 0) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">What Clients Say</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              No testimonials available at the moment.\n            </p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">What Clients Say</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Don&apos;t just take my word for it. Here&apos;s what some of my clients have to say\n            about working with me.\n          </p>\n        </motion.div>\n\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main Testimonial Card */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={inView ? { opacity: 1, scale: 1 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <Card className=\"relative overflow-hidden border-border/50 bg-gradient-to-br from-background to-muted/30\">\n              <div className=\"absolute top-6 left-6 text-primary/20\">\n                <Quote className=\"h-12 w-12\" />\n              </div>\n              \n              <CardContent className=\"p-8 md:p-12\">\n                <AnimatePresence mode=\"wait\">\n                  <motion.div\n                    key={currentIndex}\n                    initial={{ opacity: 0, x: 50 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -50 }}\n                    transition={{ duration: 0.5 }}\n                    className=\"text-center\"\n                  >\n                    {/* Rating Stars */}\n                    <div className=\"flex justify-center mb-6\">\n                      {[...Array(currentTestimonial.rating)].map((_, i) => (\n                        <Star key={i} className=\"h-5 w-5 fill-yellow-400 text-yellow-400\" />\n                      ))}\n                    </div>\n\n                    {/* Testimonial Content */}\n                    <blockquote className=\"text-lg md:text-xl text-foreground/90 mb-8 leading-relaxed\">\n                      &quot;{currentTestimonial.content}&quot;\n                    </blockquote>\n\n                    {/* Client Info */}\n                    <div className=\"flex items-center justify-center space-x-4\">\n                      <Avatar className=\"h-16 w-16 border-2 border-border\">\n                        <AvatarImage src={currentTestimonial.avatar} alt={currentTestimonial.name} />\n                        <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold\">\n                          {currentTestimonial.name.split(' ').map((n: string) => n[0]).join('')}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div className=\"text-left\">\n                        <div className=\"font-semibold text-foreground\">{currentTestimonial.name}</div>\n                        <div className=\"text-sm text-muted-foreground\">{currentTestimonial.role}</div>\n                        <div className=\"text-sm text-primary\">{currentTestimonial.company}</div>\n                      </div>\n                    </div>\n                  </motion.div>\n                </AnimatePresence>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Navigation Controls */}\n          <motion.div\n            className=\"flex items-center justify-center space-x-4 mt-8\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={inView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              onClick={prevTestimonial}\n              className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n            >\n              <ChevronLeft className=\"h-4 w-4\" />\n            </Button>\n\n            {/* Dots Indicator */}\n            <div className=\"flex space-x-2\">\n              {testimonials.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setCurrentIndex(index)}\n                  className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                    index === currentIndex \n                      ? 'bg-primary w-8' \n                      : 'bg-muted-foreground/30 hover:bg-muted-foreground/50'\n                  }`}\n                />\n              ))}\n            </div>\n\n            <Button\n              variant=\"outline\"\n              size=\"icon\"\n              onClick={nextTestimonial}\n              className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n            >\n              <ChevronRight className=\"h-4 w-4\" />\n            </Button>\n          </motion.div>\n\n          {/* Testimonial Grid Preview */}\n          <motion.div\n            className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mt-12\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={inView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.6 }}\n          >\n            {testimonials.map((testimonial, index) => (\n              <motion.button\n                key={testimonial.id}\n                onClick={() => setCurrentIndex(index)}\n                className={`p-3 rounded-lg border transition-all duration-300 ${\n                  index === currentIndex\n                    ? 'border-primary bg-primary/10 scale-105'\n                    : 'border-border hover:border-border/80 hover:bg-accent'\n                }`}\n                whileHover={{ scale: index === currentIndex ? 1.05 : 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <Avatar className=\"h-12 w-12 mx-auto mb-2\">\n                  <AvatarImage src={testimonial.avatar} alt={testimonial.name} />\n                  <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-600 text-white text-sm\">\n                    {testimonial.name.split(' ').map(n => n[0]).join('')}\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"text-xs font-medium text-center\">{testimonial.name}</div>\n                <div className=\"text-xs text-muted-foreground text-center\">{testimonial.company}</div>\n              </motion.button>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI;gBACF,aAAa;gBACb,MAAM,OAAO,MAAM,iHAAA,CAAA,MAAG,CAAC,eAAe;gBACtC,gBAAgB,CAAA,GAAA,iHAAA,CAAA,2BAAwB,AAAD,EAAE;gBACzC,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D,GAAG;QAAC,aAAa,MAAM;KAAC;IAExB,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,KAAK,GAAG;QAC/B,MAAM,WAAW,YAAY,iBAAiB;QAC9C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB,aAAa,MAAM;KAAC;IAEzC,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;IAK7B;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,wBAAwB;IACxB,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE,IAAI,CAAC;4BAC9C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAGnB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4CAAC,MAAK;sDACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAGV,8OAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM,mBAAmB,MAAM;yDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7C,8OAAC,kMAAA,CAAA,OAAI;gEAAS,WAAU;+DAAb;;;;;;;;;;kEAKf,8OAAC;wDAAW,WAAU;;4DAA6D;4DAC1E,mBAAmB,OAAO;4DAAC;;;;;;;kEAIpC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,8OAAC,kIAAA,CAAA,cAAW;wEAAC,KAAK,mBAAmB,MAAM;wEAAE,KAAK,mBAAmB,IAAI;;;;;;kFACzE,8OAAC,kIAAA,CAAA,iBAAc;wEAAC,WAAU;kFACvB,mBAAmB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;0EAGtE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiC,mBAAmB,IAAI;;;;;;kFACvE,8OAAC;wEAAI,WAAU;kFAAiC,mBAAmB,IAAI;;;;;;kFACvE,8OAAC;wEAAI,WAAU;kFAAwB,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;;+CA9BhE;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwCf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAIzB,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,mBACA,uDACJ;2CANG;;;;;;;;;;8CAWX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAEvC,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,kDAAkD,EAC5D,UAAU,eACN,2CACA,wDACJ;oCACF,YAAY;wCAAE,OAAO,UAAU,eAAe,OAAO;oCAAK;oCAC1D,UAAU;wCAAE,OAAO;oCAAK;;sDAExB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,YAAY,MAAM;oDAAE,KAAK,YAAY,IAAI;;;;;;8DAC3D,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;sDAGrD,8OAAC;4CAAI,WAAU;sDAAmC,YAAY,IAAI;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDAA6C,YAAY,OAAO;;;;;;;mCAjB1E,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBnC", "debugId": null}}, {"offset": {"line": 5173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/contact-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport * as z from \"zod\";\nimport { \n  Mail, \n  Phone, \n  MapPin, \n  Send, \n  Clock,\n  Globe,\n  Linkedin,\n  Github,\n  Twitter\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { toast } from \"sonner\";\n\nconst contactSchema = z.object({\n  name: z.string().min(2, \"Name must be at least 2 characters\"),\n  email: z.string().email(\"Please enter a valid email address\"),\n  subject: z.string().min(5, \"Subject must be at least 5 characters\"),\n  message: z.string().min(10, \"Message must be at least 10 characters\"),\n});\n\ntype ContactForm = z.infer<typeof contactSchema>;\n\nconst contactInfo = [\n  {\n    icon: Mail,\n    label: \"Email\",\n    value: \"<EMAIL>\",\n    href: \"mailto:<EMAIL>\",\n  },\n  {\n    icon: Phone,\n    label: \"Phone\",\n    value: \"+9779810580378\",\n    href: \"tel:+9779810580378\",\n  },\n  {\n    icon: MapPin,\n    label: \"Location\",\n    value: \"Kathmandu, Nepal\",\n    href: \"https://maps.google.com/?q=Kathmandu,Nepal\",\n  },\n  {\n    icon: Clock,\n    label: \"Timezone\",\n    value: \"NPT (UTC+5:45)\",\n    href: null,\n  },\n];\n\nconst socialLinks = [\n  {\n    icon: Linkedin,\n    label: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat0\",\n    color: \"text-blue-600 hover:text-blue-700\",\n  },\n  {\n    icon: Github,\n    label: \"GitHub\",\n    href: \"https://github.com/ash-333\",\n    color: \"text-gray-800 dark:text-gray-200 hover:text-gray-600 dark:hover:text-gray-400\",\n  },\n  {\n    icon: Twitter,\n    label: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat7\",\n    color: \"text-blue-500 hover:text-blue-600\",\n  },\n  {\n    icon: Globe,\n    label: \"Website\",\n    href: \"https://ashishkamat.com/np\",\n    color: \"text-green-600 hover:text-green-700\",\n  },\n];\n\nexport function ContactSection() {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: { errors },\n  } = useForm<ContactForm>({\n    resolver: zodResolver(contactSchema),\n  });\n\n  const onSubmit = async (data: ContactForm) => {\n    setIsSubmitting(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      console.log(\"Form submitted:\", data);\n      toast.success(\"Message sent successfully! I&apos;ll get back to you soon.\");\n      reset();\n    } catch {\n      toast.error(\"Failed to send message. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Let&apos;s Work Together</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Have a project in mind? I&apos;d love to hear about it.\n            Let&apos;s discuss how we can bring your ideas to life.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto\">\n          {/* Contact Information */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold mb-6\">Get in Touch</h3>\n              <p className=\"text-muted-foreground mb-8\">\n                I&apos;m always open to discussing new opportunities, creative projects,\n                or potential collaborations. Feel free to reach out!\n              </p>\n            </div>\n\n            {/* Contact Info Cards */}\n            <div className=\"grid sm:grid-cols-2 gap-4\">\n              {contactInfo.map((info, index) => {\n                const Icon = info.icon;\n                const content = (\n                  <Card className=\"hover-lift transition-all duration-300 border-border/50 hover:border-border\">\n                    <CardContent className=\"p-4\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center\">\n                          <Icon className=\"h-5 w-5 text-primary\" />\n                        </div>\n                        <div>\n                          <div className=\"text-sm text-muted-foreground\">{info.label}</div>\n                          <div className=\"font-medium\">{info.value}</div>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                );\n\n                return (\n                  <motion.div\n                    key={info.label}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={inView ? { opacity: 1, y: 0 } : {}}\n                    transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\n                  >\n                    {info.href ? (\n                      <a href={info.href} target=\"_blank\" rel=\"noopener noreferrer\">\n                        {content}\n                      </a>\n                    ) : (\n                      content\n                    )}\n                  </motion.div>\n                );\n              })}\n            </div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              <h4 className=\"font-semibold mb-4\">Connect with me</h4>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social, index) => {\n                  const Icon = social.icon;\n                  return (\n                    <motion.a\n                      key={social.label}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`p-3 rounded-full bg-background border border-border hover:border-primary/50 transition-all duration-300 ${social.color}`}\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                      initial={{ opacity: 0, scale: 0 }}\n                      animate={inView ? { opacity: 1, scale: 1 } : {}}\n                      transition={{ duration: 0.3, delay: 0.8 + index * 0.1 }}\n                    >\n                      <Icon className=\"h-5 w-5\" />\n                    </motion.a>\n                  );\n                })}\n              </div>\n            </motion.div>\n\n            {/* Availability */}\n            <motion.div\n              className=\"bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-6 border border-border/50\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: 0.8 }}\n            >\n              <div className=\"flex items-center space-x-3 mb-2\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\" />\n                <span className=\"font-semibold text-green-700 dark:text-green-400\">Available for new projects</span>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                I&apos;m currently accepting new client work and interesting project collaborations.\n              </p>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <Card className=\"border-border/50\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Send className=\"h-5 w-5\" />\n                  <span>Send me a message</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n                  <div className=\"grid sm:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"name\">Name *</Label>\n                      <Input\n                        id=\"name\"\n                        placeholder=\"Your name\"\n                        {...register(\"name\")}\n                        className={errors.name ? \"border-destructive\" : \"\"}\n                      />\n                      {errors.name && (\n                        <p className=\"text-sm text-destructive\">{errors.name.message}</p>\n                      )}\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"email\">Email *</Label>\n                      <Input\n                        id=\"email\"\n                        type=\"email\"\n                        placeholder=\"<EMAIL>\"\n                        {...register(\"email\")}\n                        className={errors.email ? \"border-destructive\" : \"\"}\n                      />\n                      {errors.email && (\n                        <p className=\"text-sm text-destructive\">{errors.email.message}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"subject\">Subject *</Label>\n                    <Input\n                      id=\"subject\"\n                      placeholder=\"Project inquiry, collaboration, etc.\"\n                      {...register(\"subject\")}\n                      className={errors.subject ? \"border-destructive\" : \"\"}\n                    />\n                    {errors.subject && (\n                      <p className=\"text-sm text-destructive\">{errors.subject.message}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"message\">Message *</Label>\n                    <Textarea\n                      id=\"message\"\n                      placeholder=\"Tell me about your project, timeline, budget, and any specific requirements...\"\n                      rows={6}\n                      {...register(\"message\")}\n                      className={errors.message ? \"border-destructive\" : \"\"}\n                    />\n                    {errors.message && (\n                      <p className=\"text-sm text-destructive\">{errors.message.message}</p>\n                    )}\n                  </div>\n\n                  <Button \n                    type=\"submit\" \n                    className=\"w-full group\" \n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                        Sending...\n                      </>\n                    ) : (\n                      <>\n                        <Send className=\"mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                        Send Message\n                      </>\n                    )}\n                  </Button>\n                </form>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AAxBA;;;;;;;;;;;;;;;AA0BA,MAAM,gBAAgB,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,EAAE;IAC7B,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACxB,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;IACxB,SAAS,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC3B,SAAS,CAAA,GAAA,iJAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI;AAC9B;AAIA,MAAM,cAAc;IAClB;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAO5C,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;wCACtB,MAAM,OAAO,KAAK,IAAI;wCACtB,MAAM,wBACJ,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;;;;;;;;;;;sEAElB,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAiC,KAAK,KAAK;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAAe,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAOlD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI,CAAC;4CAC1C,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;sDAErD,KAAK,IAAI,iBACR,8OAAC;gDAAE,MAAM,KAAK,IAAI;gDAAE,QAAO;gDAAS,KAAI;0DACrC;;;;;uDAGH;2CAVG,KAAK,KAAK;;;;;oCAcrB;;;;;;8CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,wGAAwG,EAAE,OAAO,KAAK,EAAE;oDACpI,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,SAAS,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE,IAAI,CAAC;oDAC9C,YAAY;wDAAE,UAAU;wDAAK,OAAO,MAAM,QAAQ;oDAAI;8DAEtD,cAAA,8OAAC;wDAAK,WAAU;;;;;;mDAXX,OAAO,KAAK;;;;;4CAcvB;;;;;;;;;;;;8CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC1C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAmD;;;;;;;;;;;;sDAErE,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAOjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAK,UAAU,aAAa;4CAAW,WAAU;;8DAChD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAO;;;;;;8EACtB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,aAAY;oEACX,GAAG,SAAS,OAAO;oEACpB,WAAW,OAAO,IAAI,GAAG,uBAAuB;;;;;;gEAEjD,OAAO,IAAI,kBACV,8OAAC;oEAAE,WAAU;8EAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sEAGhE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAQ;;;;;;8EACvB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,aAAY;oEACX,GAAG,SAAS,QAAQ;oEACrB,WAAW,OAAO,KAAK,GAAG,uBAAuB;;;;;;gEAElD,OAAO,KAAK,kBACX,8OAAC;oEAAE,WAAU;8EAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;8DAKnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,aAAY;4DACX,GAAG,SAAS,UAAU;4DACvB,WAAW,OAAO,OAAO,GAAG,uBAAuB;;;;;;wDAEpD,OAAO,OAAO,kBACb,8OAAC;4DAAE,WAAU;sEAA4B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8DAInE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAU;;;;;;sEACzB,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,aAAY;4DACZ,MAAM;4DACL,GAAG,SAAS,UAAU;4DACvB,WAAW,OAAO,OAAO,GAAG,uBAAuB;;;;;;wDAEpD,OAAO,OAAO,kBACb,8OAAC;4DAAE,WAAU;sEAA4B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8DAInE,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,UAAU;8DAET,6BACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAAmE;;qFAIpF;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA6E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarH", "debugId": null}}, {"offset": {"line": 5949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ash-333\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://www.linkedin.com/in/ashishkamat0/\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat4\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Kathmandu</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,8OAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}