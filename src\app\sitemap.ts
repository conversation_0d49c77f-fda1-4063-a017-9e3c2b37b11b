import { MetadataRoute } from 'next'

export const dynamic = 'force-dynamic'
export const revalidate = 0

const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'https://cms.ashishkamat.com.np'
const BLOG_API_URL = `${CMS_BASE_URL}/api/blog`
const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://ashishkamat.com.np'

type SitemapEntry = {
  url: string
  lastModified?: string | Date
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority?: number
}

function createSitemapEntry(
  url: string,
  lastModified: Date,
  changeFrequency: SitemapEntry['changeFrequency'],
  priority: number
): SitemapEntry {
  return { url, lastModified, changeFrequency, priority }
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    const res = await fetch(BLOG_API_URL, {
      next: { revalidate: 0 },
    })
    if (!res.ok) throw new Error('Failed to fetch blog posts')

    const posts: {
      slug: string
      updatedAt: string
      published: boolean
    }[] = await res.json()

    const staticUrls: SitemapEntry[] = [
      createSitemapEntry(baseUrl, new Date(), 'weekly', 1),
      createSitemapEntry(`${baseUrl}/about`, new Date(), 'monthly', 0.8),
      createSitemapEntry(`${baseUrl}/projects`, new Date(), 'weekly', 0.9),
      createSitemapEntry(`${baseUrl}/blog`, new Date(), 'weekly', 0.7),
      createSitemapEntry(`${baseUrl}/contact`, new Date(), 'monthly', 0.6),
    ]

    const blogUrls: SitemapEntry[] = posts
      .filter((post) => post.published)
      .map((post) =>
        createSitemapEntry(
          `${baseUrl}/blog/${post.slug}`,
          new Date(post.updatedAt),
          'weekly',
          0.8
        )
      )

    return [...staticUrls, ...blogUrls]
  } catch (error) {
    console.error('Sitemap generation failed:', error)
    return []
  }
}
